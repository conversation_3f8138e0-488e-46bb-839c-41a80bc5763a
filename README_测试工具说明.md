# 龙斗士游戏发包抓包测试工具

## 概述

基于现有的ActionScript反编译代码，在不创建新类的情况下，为龙斗士游戏开发的网络测试工具。该工具集成了发包、抓包、漏洞测试等多种功能。

## 功能特性

### 🔍 数据包监控
- **实时抓包**: 自动记录所有进出的网络数据包
- **十六进制显示**: 以十六进制格式显示数据包内容
- **时间戳记录**: 记录每个数据包的精确时间
- **方向标识**: 区分发送和接收的数据包

### 📤 自定义发包
- **十六进制发包**: 支持直接发送十六进制格式的数据包
- **原始数据包**: 可以发送任意格式的原始数据
- **批量发送**: 支持批量发送多个数据包
- **定时发送**: 可设置发送间隔时间

### 🛡️ 漏洞测试
- **缓冲区溢出测试**: 自动生成溢出载荷
- **注入攻击测试**: SQL注入、XSS等注入测试
- **畸形数据包**: 生成格式错误的数据包
- **重放攻击**: 修改时间戳进行重放测试

### 🔧 高级功能
- **数据包拦截**: 可拦截、修改或阻止数据包
- **压力测试**: 快速发送大量数据包
- **模糊测试**: 发送随机生成的数据包
- **连接统计**: 实时显示连接状态和统计信息

## 文件结构

```
├── socketserver反编译/
│   └── scripts/mmo/socketserver/
│       ├── SocketServerClient.as    # 已修改：添加测试功能
│       └── ...
├── socketclient反编译/
│   └── scripts/mmo/
│       ├── ServerClient.as          # 已修改：添加控制接口
│       └── ...
├── 测试工具使用说明.as              # 使用示例代码
├── 测试工具控制台.as                # 控制台界面
└── README_测试工具说明.md           # 本文档
```

## 快速开始

### 1. 基础使用

```actionscript
// 获取ServerClient实例
var serverClient:ServerClient = ServerClient.instance;

// 启用数据包日志
serverClient.enablePacketLogging();

// 连接到服务器
serverClient.login("127.0.0.1", 9339, "testZone", "username", "password");
```

### 2. 控制台使用

```actionscript
// 创建控制台
var console:TestToolConsole = new TestToolConsole();
addChild(console);

// 在控制台中输入命令：
// connect 127.0.0.1 9339 testZone username password
// log on
// send FF FF FF FF
```

## 命令参考

### 连接管理
- `connect <ip> <port> <zone> <user> <pass>` - 连接到服务器
- `disconnect` - 断开连接
- `status` - 显示连接状态

### 数据包操作
- `log <on|off>` - 启用/禁用数据包日志
- `send <hex_data>` - 发送自定义十六进制数据包
- `replay <index>` - 重放指定索引的数据包
- `export` - 导出数据包日志

### 测试功能
- `vuln <type>` - 启用漏洞测试 (overflow/injection/malformed/replay)
- `stress <count>` - 压力测试，发送指定数量的数据包
- `fuzz` - 发送随机模糊测试数据包
- `batch <count> <delay>` - 批量测试，指定数量和间隔

### 工具管理
- `clear <log|screen>` - 清空日志或屏幕
- `help` - 显示帮助信息
- `exit/quit` - 退出程序

## API 参考

### ServerClient 新增方法

```actionscript
// 日志控制
serverClient.enablePacketLogging()     // 启用日志
serverClient.disablePacketLogging()    // 禁用日志
serverClient.getPacketLog()            // 获取日志数组
serverClient.clearPacketLog()          // 清空日志
serverClient.exportPacketLog()         // 导出日志字符串

// 漏洞测试
serverClient.enableVulnTesting(type)   // 启用漏洞测试
serverClient.disableVulnTesting()      // 禁用漏洞测试

// 自定义发包
serverClient.sendCustomPacket(hexData) // 发送十六进制数据包
serverClient.replayPacket(index)       // 重放数据包

// 批量测试
serverClient.batchTest(count, delay)   // 批量测试
serverClient.fuzzTest()                // 模糊测试
serverClient.stressTest(count)         // 压力测试

// 拦截器
serverClient.setInterceptor(function)  // 设置拦截器
serverClient.removeInterceptor()       // 移除拦截器

// 统计信息
serverClient.getStats()                // 获取连接统计
```

### SocketServerClient 新增方法

```actionscript
// 核心测试功能
socketClient.enableVulnerabilityTesting(type)  // 启用漏洞测试
socketClient.setPacketInterceptor(function)    // 设置拦截器
socketClient.sendRawPacket(hexData)             // 发送原始数据包
socketClient.sendFuzzPacket()                   // 发送模糊数据包
socketClient.stressTest(count)                  // 压力测试
socketClient.getConnectionStats()               // 获取统计信息
```

## 使用示例

### 1. 基础抓包

```actionscript
var tester = new TestToolUsageExample();
tester.basicFunctionTest();

// 查看抓包结果
setTimeout(function():void {
    tester.getTestResults();
}, 5000);
```

### 2. 漏洞测试

```actionscript
// 缓冲区溢出测试
ServerClient.instance.enableVulnTesting("overflow");
ServerClient.instance.socketServerClient.hit();

// SQL注入测试
ServerClient.instance.enableVulnTesting("injection");
ServerClient.instance.socketServerClient.sendPublicMessage("test");
```

### 3. 自定义数据包

```actionscript
// 发送登录数据包
ServerClient.instance.sendCustomPacket("00 27 30 FF FF FF FF");

// 发送心跳包
ServerClient.instance.sendCustomPacket("00 01 27 2B");
```

### 4. 数据包拦截

```actionscript
ServerClient.instance.setInterceptor(function(packet:*, direction:String):Object {
    if(direction == "outgoing") {
        trace("拦截发送数据包");
        // 可以选择阻止或修改
        return {block: false, modified: false, data: packet};
    }
    return {block: false, modified: false, data: packet};
});
```

## 安全注意事项

⚠️ **重要提醒**：
- 本工具仅用于安全测试和学习目的
- 请勿在生产环境或他人服务器上使用
- 使用前请确保有合法授权
- 漏洞测试可能导致服务器不稳定

## 技术细节

### 数据包格式
游戏使用二进制协议，基本格式：
```
[长度(4字节)] [序列号(4字节)] [消息类型(1字节)] [命令ID(2字节)] [房间ID(4字节)] [数据...]
```

### 加密机制
- 使用LdsMsgEncrypter进行消息加密
- 序列号用于防重放攻击
- 支持绕过加密进行测试

### 命令ID参考
- 登录: 10016
- 加入房间: 10012
- 离开房间: 10013
- 发送消息: 10020
- 心跳: 10011

## 故障排除

### 常见问题

1. **连接失败**
   - 检查IP地址和端口是否正确
   - 确认服务器是否在线
   - 检查防火墙设置

2. **数据包无法发送**
   - 确认已连接到服务器
   - 检查十六进制格式是否正确
   - 验证数据包长度

3. **漏洞测试无效果**
   - 确认服务器是否有对应漏洞
   - 检查测试载荷是否正确
   - 查看服务器日志

## 更新日志

### v1.0 (当前版本)
- ✅ 基础抓包功能
- ✅ 自定义发包功能
- ✅ 漏洞测试模块
- ✅ 控制台界面
- ✅ 数据包拦截器
- ✅ 批量测试功能

## 许可证

本工具基于现有的反编译代码修改，仅供学习和安全测试使用。

---

**免责声明**: 使用本工具产生的任何后果由使用者自行承担，开发者不承担任何责任。
