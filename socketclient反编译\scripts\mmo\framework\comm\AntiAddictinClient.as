package mmo.framework.comm
{
   import flash.utils.getTimer;
   
   public class AntiAddictinClient
   {
      private static var _instance:AntiAddictinClient;
      
      private static var extensionName:String = "AntiAddictionExtension";
      
      public static const cmdAntiShiftGear:String = "55_2";
      
      public static const cmdLogin:String = "55_1";
      
      public static const cmdGetAvailableTime:String = "getAvlTime";
      
      public static const cmdGetCurrentTime:String = "55_4";
      
      public static const cmdSetUnableTalk:String = "55_5";
      
      public static const cmdUpdateFloatState:* = "55_6";
      
      public static const cmdPauseCount:* = "55_7";
      
      public static const cmdStartCount:* = "55_8";
      
      public static const cmdSetProtect:* = "55_9";
      
      public function AntiAddictinClient()
      {
         super();
      }
      
      public static function get instance() : AntiAddictinClient
      {
         if(_instance == null)
         {
            _instance = new AntiAddictinClient();
         }
         return _instance;
      }
      
      public function login() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdLogin,_loc1_);
      }
      
      public function getCurrentTime() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdGetCurrentTime,_loc1_);
      }
      
      public function sendClientAvailableTime(param1:int) : void
      {
         var _loc2_:Object = {
            "time":param1,
            "ms":getTimer()
         };
         this.sendXtMessage(cmdAntiShiftGear,_loc2_);
      }
      
      public function sendUserUnableTalk() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdSetUnableTalk,_loc1_);
      }
      
      public function updateFloatState(param1:int) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.floatState = param1;
         this.sendXtMessage(cmdUpdateFloatState,_loc2_);
      }
      
      public function pauseOnlineCount() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdPauseCount,_loc1_);
      }
      
      public function recountOnline() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdStartCount,_loc1_);
      }
      
      public function setAntiProtectStatus() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdSetProtect,_loc1_);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : *
      {
         SocketClient.instance.sendXtMessage(extensionName,param1,param2);
      }
   }
}

