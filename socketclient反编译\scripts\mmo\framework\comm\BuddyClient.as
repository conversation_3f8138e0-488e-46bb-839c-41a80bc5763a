package mmo.framework.comm
{
   import flash.events.Event;
   
   public class BuddyClient
   {
      private static var _instance:BuddyClient;
      
      private static const EXTENSION_NAME:String = ExtMap.BuddyExtension;
      
      private const cmdBuddyRequest:String = "1000_0";
      
      private const cmdHandleRequest:* = "1000_1";
      
      private const onBuddyAddedResponse:* = "1000_2";
      
      private const cmdLoadBuddyList:* = "1000_3";
      
      private const cmdLoadBlockList:* = "1000_4";
      
      private const cmdDeleteBuddy:* = "1000_5";
      
      private const cmdChangeBlock:* = "1000_6";
      
      private const onBuddyStateUpdateReponse:* = "1000_7";
      
      private const cmdRemarkBuddy:* = "1000_20";
      
      private const cmdChangeGroupName:* = "1000_18";
      
      private const cmdGetStrangerInZone:* = "1000_30";
      
      private const cmdGetCidsByDuoduo:* = "1000_31";
      
      private const cmdSetGroupBatch:* = "1000_21";
      
      private const cmdCreateGroup:* = "1000_17";
      
      private const cmdDeleteGroup:* = "1000_19";
      
      public var buddyList:Array = [];
      
      public var blockList:Array = [];
      
      public var groupList:Array = [];
      
      public function BuddyClient()
      {
         super();
         SocketClient.instance.addEventListener(this.cmdBuddyRequest,this.onBuddyRequest);
         SocketClient.instance.addEventListener(this.cmdHandleRequest,this.onHandleRequest);
         SocketClient.instance.addEventListener(this.onBuddyAddedResponse,this.onBuddyAdded);
         SocketClient.instance.addEventListener(this.cmdLoadBuddyList,this.onLoadBuddyList);
         SocketClient.instance.addEventListener(this.cmdLoadBlockList,this.onLoadBlockList);
         SocketClient.instance.addEventListener(this.cmdDeleteBuddy,this.onDeleteBuddy);
         SocketClient.instance.addEventListener(this.cmdChangeBlock,this.onChangeBlock);
         SocketClient.instance.addEventListener(this.onBuddyStateUpdateReponse,this.onBuddyStateUpdate);
      }
      
      public static function get instance() : BuddyClient
      {
         if(_instance == null)
         {
            _instance = new BuddyClient();
         }
         return _instance;
      }
      
      private function onBuddyRequest(param1:SocketClientEvent) : void
      {
         var _loc2_:Event = new SocketClientEvent(SocketClientEvent.onBuddyPermissionRequest,{"sender":param1.params.sender});
         SocketClient.instance.dispatchEvent(_loc2_);
      }
      
      private function onHandleRequest(param1:SocketClientEvent) : void
      {
         var _loc2_:String = "添加好友错误";
         switch(int(param1.params.err))
         {
            case 1:
               _loc2_ = "你的好友列表已满。";
               break;
            case 2:
               _loc2_ = param1.params.u + "的好友列表已满。";
               break;
            case 3:
               _loc2_ = "无法验证好友请求。";
               break;
            case 5:
               _loc2_ = "你们已经是好友了。";
         }
         var _loc3_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onBuddyListError,{"error":_loc2_});
         SocketClient.instance.dispatchEvent(_loc3_);
      }
      
      private function onBuddyAdded(param1:SocketClientEvent) : void
      {
         var _loc2_:Object = this.getBuddyFromMsgBuddy(param1.params.b);
         this.buddyList.push(_loc2_);
         var _loc3_:Object = {};
         _loc3_.list = this.buddyList;
         _loc3_.cause = "added";
         _loc3_.newBuddy = _loc2_.name;
         param1 = new SocketClientEvent(SocketClientEvent.onBuddyList,_loc3_);
         SocketClient.instance.dispatchEvent(param1);
      }
      
      private function getBuddyFromMsgBuddy(param1:Object) : Object
      {
         var _loc2_:Object = {};
         _loc2_.characterId = param1.bi;
         _loc2_.duoduoId = param1.di;
         _loc2_.name = param1.n;
         _loc2_.isOnline = param1.o;
         _loc2_.group = param1.g;
         _loc2_.memberType = param1.mt;
         _loc2_.isBlocked = false;
         _loc2_.zoneName = param1.z;
         _loc2_.remark = param1.rm;
         _loc2_.showName = _loc2_.remark == null || _loc2_.remark == "" ? _loc2_.name : _loc2_.remark;
         return _loc2_;
      }
      
      private function onLoadBuddyList(param1:SocketClientEvent) : void
      {
         var _loc4_:Object = null;
         var _loc5_:* = undefined;
         var _loc6_:Object = null;
         var _loc7_:int = 0;
         var _loc8_:String = null;
         var _loc9_:Object = null;
         var _loc10_:Object = null;
         this.buddyList = [];
         var _loc2_:Object = {};
         var _loc3_:Array = param1.params.cbi.gl;
         for each(_loc4_ in _loc3_)
         {
            _loc7_ = int(_loc4_.gid);
            _loc8_ = _loc4_.gn;
            _loc2_[_loc7_] = _loc8_;
            for each(_loc9_ in _loc4_.bl)
            {
               _loc10_ = this.getBuddyFromMsgBuddy(_loc9_);
               _loc10_.groupName = _loc8_;
               if(!this.checkBuddyDuplicates(_loc10_.name))
               {
                  this.buddyList.push(_loc10_);
               }
            }
         }
         this.groupList = [];
         for(_loc5_ in _loc2_)
         {
            this.groupList.push({
               "group":int(_loc5_),
               "groupName":_loc2_[_loc5_]
            });
         }
         _loc6_ = {};
         _loc6_.list = this.buddyList;
         _loc6_.cause = "loaded";
         param1 = new SocketClientEvent(SocketClientEvent.onBuddyList,_loc6_);
         SocketClient.instance.dispatchEvent(param1);
      }
      
      private function onLoadBlockList(param1:SocketClientEvent) : void
      {
         var _loc2_:Array = param1.params.bl;
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            this.blockList.push({"name":String(_loc2_[_loc3_])});
            _loc3_++;
         }
         var _loc4_:Object = {};
         _loc4_.list = this.blockList;
         _loc4_.cause = "loaded";
         param1 = new SocketClientEvent(SocketClientEvent.onBlockList,_loc4_);
         SocketClient.instance.dispatchEvent(param1);
      }
      
      private function onDeleteBuddy(param1:SocketClientEvent) : void
      {
         var _loc4_:Object = null;
         var _loc5_:SocketClientEvent = null;
         var _loc2_:String = param1.params.b;
         var _loc3_:Boolean = this.__removeUserFormList(_loc2_,this.buddyList);
         if(_loc3_)
         {
            _loc4_ = {};
            _loc4_.list = this.buddyList;
            _loc4_.cause = "beRemoved";
            _loc5_ = new SocketClientEvent(SocketClientEvent.onBuddyList,_loc4_);
            SocketClient.instance.dispatchEvent(_loc5_);
         }
      }
      
      private function onChangeBlock(param1:SocketClientEvent) : void
      {
         var _loc2_:String = param1.params.u;
         var _loc3_:int = int(param1.params.a);
         switch(_loc3_)
         {
            case 1:
               if(!this.isBlockExist(_loc2_))
               {
                  this.blockList.push({"name":_loc2_});
               }
               break;
            case -1:
               this.__removeUserFormList(_loc2_,this.blockList);
         }
         var _loc4_:Object = {};
         _loc4_.list = this.blockList;
         _loc4_.cause = "changed";
         param1 = new SocketClientEvent(SocketClientEvent.onBlockList,_loc4_);
         SocketClient.instance.dispatchEvent(param1);
      }
      
      public function addBuddyRequest(param1:String) : void
      {
         if(param1 == SocketClient.instance.myUserName || this.checkBuddyDuplicates(param1))
         {
            return;
         }
         this.sendXtMessage(this.cmdBuddyRequest,{"u":param1});
      }
      
      public function updateBuddyGroup(param1:String, param2:int) : void
      {
         var _loc3_:Object = null;
         for each(_loc3_ in this.buddyList)
         {
            if(_loc3_.name == param1)
            {
               _loc3_.group = param2;
               return;
            }
         }
      }
      
      public function sendBuddyPermissionResponse(param1:Boolean, param2:String) : void
      {
         this.handleAddBuddyRequest(param2,param1 ? 0 : 1);
      }
      
      public function handleAddBuddyRequest(param1:String, param2:int) : void
      {
         this.sendXtMessage(this.cmdHandleRequest,{
            "u":param1,
            "a":param2
         });
      }
      
      public function loadBuddyList() : void
      {
         this.sendXtMessage(this.cmdLoadBuddyList,null);
      }
      
      public function loadBlockList() : void
      {
         this.sendXtMessage(this.cmdLoadBlockList,null);
      }
      
      public function removeBuddy(param1:int, param2:String) : void
      {
         var _loc4_:Object = null;
         var _loc5_:SocketClientEvent = null;
         var _loc3_:Boolean = this.__removeUserFormList(param2,this.buddyList);
         if(_loc3_)
         {
            this.sendXtMessage(this.cmdDeleteBuddy,{
               "gid":param1,
               "u":param2
            });
            _loc4_ = {};
            _loc4_.list = this.buddyList;
            _loc4_.cause = "removed";
            _loc5_ = new SocketClientEvent(SocketClientEvent.onBuddyList,_loc4_);
            SocketClient.instance.dispatchEvent(_loc5_);
         }
      }
      
      public function setBuddyBlockStatus(param1:String, param2:Boolean) : void
      {
         this.changeBlockUser(param1,param2 ? 1 : -1);
      }
      
      public function changeBlockUser(param1:String, param2:int) : void
      {
         var _loc4_:Object = null;
         var _loc5_:SocketClientEvent = null;
         if(param1 == SocketClient.instance.myUserName)
         {
            return;
         }
         var _loc3_:* = this.checkBlockUserExists(param1);
         if(param2 == 1)
         {
            if(!_loc3_)
            {
               this.sendXtMessage(this.cmdChangeBlock,{
                  "u":param1,
                  "a":param2
               });
            }
         }
         else if(_loc3_)
         {
            this.sendXtMessage(this.cmdChangeBlock,{
               "u":param1,
               "a":param2
            });
            this.__removeUserFormList(param1,this.blockList);
            _loc4_ = {};
            _loc4_.list = this.blockList;
            _loc4_.cause = "removed";
            _loc5_ = new SocketClientEvent(SocketClientEvent.onBlockList,_loc4_);
            SocketClient.instance.dispatchEvent(_loc5_);
         }
      }
      
      private function onBuddyStateUpdate(param1:SocketClientEvent) : void
      {
         var _loc3_:Object = null;
         var _loc2_:Object = {};
         _loc2_.name = param1.params.n;
         _loc2_.isOnline = param1.params.o;
         _loc2_.zoneName = param1.params.z;
         var _loc4_:Boolean = false;
         var _loc5_:int = 0;
         while(_loc5_ < this.buddyList.length)
         {
            _loc3_ = this.buddyList[_loc5_];
            if(_loc3_.name == _loc2_.name)
            {
               _loc3_.isOnline = _loc2_.isOnline;
               _loc3_.zoneName = _loc2_.zoneName;
               _loc4_ = true;
               break;
            }
            _loc5_++;
         }
         if(!_loc4_)
         {
            return;
         }
         var _loc6_:Object = {};
         _loc6_.buddy = _loc3_;
         var _loc7_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onBuddyOnline,_loc2_);
         SocketClient.instance.dispatchEvent(_loc7_);
         param1 = new SocketClientEvent(SocketClientEvent.onBuddyListUpdate,_loc6_);
         SocketClient.instance.dispatchEvent(param1);
      }
      
      public function getBuddyByName(param1:String) : Object
      {
         var _loc2_:Object = null;
         for each(_loc2_ in this.buddyList)
         {
            if(_loc2_.name == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function isBuddyExist(param1:String) : Boolean
      {
         return this.checkBuddyDuplicates(param1);
      }
      
      public function isBlockExist(param1:String) : Boolean
      {
         return this.checkBlockUserExists(param1);
      }
      
      private function checkBuddyDuplicates(param1:String) : Boolean
      {
         var _loc3_:Object = null;
         var _loc2_:Boolean = false;
         for each(_loc3_ in this.buddyList)
         {
            if(_loc3_.name == param1)
            {
               _loc2_ = true;
               break;
            }
         }
         return _loc2_;
      }
      
      private function checkBlockUserExists(param1:String) : Boolean
      {
         var _loc3_:Object = null;
         var _loc2_:Boolean = false;
         for each(_loc3_ in this.blockList)
         {
            if(_loc3_.name == param1)
            {
               _loc2_ = true;
               break;
            }
         }
         return _loc2_;
      }
      
      private function __removeUserFormList(param1:String, param2:Array) : Boolean
      {
         var _loc4_:Object = null;
         var _loc3_:int = -1;
         var _loc5_:* = 0;
         while(_loc5_ < param2.length)
         {
            _loc4_ = param2[_loc5_];
            if(_loc4_.name == param1)
            {
               _loc3_ = _loc5_;
               break;
            }
            _loc5_++;
         }
         if(_loc3_ >= 0)
         {
            param2.splice(_loc3_,1);
            return true;
         }
         return false;
      }
      
      public function changeGroupName(param1:int, param2:String, param3:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdChangeGroupName,{
            "gid":param1,
            "ngn":param2
         },param3);
      }
      
      public function remarkBuddy(param1:int, param2:String, param3:String, param4:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdRemarkBuddy,{
            "gid":param1,
            "bn":param2,
            "rm":param3
         },param4);
      }
      
      public function getStrangerInZone(param1:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdGetStrangerInZone,{},param1);
      }
      
      public function getCidsByDuoduo(param1:String, param2:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdGetCidsByDuoduo,{"dd":param1},param2);
      }
      
      public function setGroupBatch(param1:int, param2:Array, param3:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdSetGroupBatch,{
            "bl":param2.join(","),
            "gid":param1
         },param3);
      }
      
      public function createGroup(param1:String, param2:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdCreateGroup,{"gn":param1},param2);
      }
      
      public function deleteGroup(param1:int, param2:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,this.cmdDeleteGroup,{"gid":param1},param2);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

