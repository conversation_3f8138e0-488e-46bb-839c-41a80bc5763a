package mmo.framework.comm
{
   public class BuddyListClient
   {
      private static var _instance:<PERSON><PERSON><PERSON>Client;
      
      private static const extensionName:* = "BuddyManagerExtension";
      
      private static const GET_BUDDY_LOCATION:* = "locateBuddy";
      
      public static const GET_BUDDY_LEVELS:String = "0_1";
      
      public static const UPDATE_BUDDY_GROUP:String = "cmdUpdateBuddyGroup";
      
      public static const CHECK_USER_EXIST:* = "checkUserExist";
      
      public static const REPORT:String = "report";
      
      public function BuddyListClient()
      {
         super();
      }
      
      public static function get instance() : BuddyListClient
      {
         if(_instance == null)
         {
            _instance = new BuddyListClient();
         }
         return _instance;
      }
      
      public function getBuddyLevels() : void
      {
         this.sendXtMessage(GET_BUDDY_LEVELS,{});
      }
      
      public function locateBuddy(param1:String) : void
      {
         this.sendXtMessage(GET_BUDDY_LOCATION,{"buddyName":param1});
      }
      
      public function updateBuddyGroup(param1:int, param2:String, param3:int) : void
      {
         var _loc4_:Object = {
            "pg":param1,
            "tg":param3,
            "bn":param2
         };
         this.sendXtMessage(UPDATE_BUDDY_GROUP,_loc4_);
      }
      
      public function checkUserExist(param1:String, param2:Object) : void
      {
         var _loc3_:Object = new Object();
         _loc3_.name = param1;
         this.sendXtMessage(CHECK_USER_EXIST,_loc3_);
      }
      
      public function sendReport(param1:String, param2:String, param3:int, param4:int) : void
      {
         var _loc5_:Object = new Object();
         _loc5_.name = param1;
         _loc5_.desc = param2;
         _loc5_.type = param3;
         _loc5_.id = param4;
         this.sendXtMessage(REPORT,_loc5_);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : *
      {
         SocketClient.instance.sendXtMessage(extensionName,param1,param2);
      }
   }
}

