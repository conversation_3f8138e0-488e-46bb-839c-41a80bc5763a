package mmo.framework.comm
{
   import flash.display.Sprite;
   import mmo.framework.comm.camp.CampCompiler;
   import mmo.framework.comm.client.PVECommonClient;
   import mmo.framework.comm.client.PVECommonResult;
   import mmo.framework.comm.corps.CorpsClient;
   import mmo.framework.comm.corps.CorpsFbClient;
   import mmo.framework.comm.corps.CorpsResult;
   import mmo.framework.comm.corps.VsCorpsClient;
   import mmo.framework.comm.corps.VsCorpsResult;
   import mmo.framework.comm.custom.CustomClient;
   import mmo.framework.comm.dragonpower.DragonPowerClient;
   import mmo.framework.comm.family.FamilyClient;
   import mmo.framework.comm.family.FamilyResult;
   import mmo.framework.comm.fb.FbClient;
   import mmo.framework.comm.festival.FestivalClient;
   import mmo.framework.comm.honor.HonorClient;
   import mmo.framework.comm.house.HouseClient;
   import mmo.framework.comm.labyrinth.LabyrinthClient;
   import mmo.framework.comm.manor.ManorCompile;
   import mmo.framework.comm.map.MapClient;
   import mmo.framework.comm.material.ClothesClient;
   import mmo.framework.comm.material.MaterialClient;
   import mmo.framework.comm.material.UsercardDecorationClient;
   import mmo.framework.comm.movement.MovementClient;
   import mmo.framework.comm.pve.PVEClient;
   import mmo.framework.comm.pvp.PvpWithSpectators_Client;
   import mmo.framework.comm.record.HttpPvRecordService;
   import mmo.framework.comm.rider.RiderDataClient;
   import mmo.framework.comm.statistics.ConstArray;
   import mmo.framework.comm.statistics.StatClient;
   import mmo.framework.comm.store.StoreClient;
   import mmo.framework.comm.transform.TransformClient;
   import mmo.framework.comm.userbehavior.UserBehaviorStatClient;
   import mmo.framework.comm.usercard.UsercardClient;
   
   public class ClientDocClass extends Sprite
   {
      public function ClientDocClass()
      {
         super();
      }
      
      private function compile() : void
      {
         ManorCompile.compileMe();
      }
   }
}

