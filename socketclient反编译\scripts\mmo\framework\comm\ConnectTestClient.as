package mmo.framework.comm
{
   public class ConnectTestClient
   {
      private static const EXT_NAME:String = "ConnectTestExtension";
      
      public static const cmdConnectTest:* = "ConnectTest";
      
      public function ConnectTestClient()
      {
         super();
      }
      
      public static function sendClientIp(param1:String, param2:Number) : void
      {
         sendXtMessage(cmdConnectTest,{
            "IP":param1,
            "t":param2
         });
      }
      
      private static function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,param1,param2);
      }
   }
}

