package mmo.framework.comm
{
   public class ExtMap
   {
      private static var em:Object = null;
      
      public static const StartupExtension:* = "StartupExtension";
      
      public static const UserExtension:* = "UserExtension";
      
      public static const BuddyManagerExtension:* = "BuddyManagerExtension";
      
      public static const Log:* = "Log";
      
      public static const SurveyExtension:* = "SurveyExtension";
      
      public static const AntiAddictionExtension:* = "AntiAddictionExtension";
      
      public static const PJKExtension:* = "PJKExtension";
      
      public static const MaterialExtension:* = "MaterialExtension";
      
      public static const SyncExtension:* = "SyncExtension";
      
      public static const TaskExtension:* = "TaskExtension";
      
      public static const CustomExtension:* = "CustomExtension";
      
      public static const MapExtension:* = "MapExtension";
      
      public static const TitleExtension:* = "TitleExtension";
      
      public static const MsgManagerExtension:* = "MsgManagerExtension";
      
      public static const SimulatorExtension:* = "SimulatorExtension";
      
      public static const FBExtension:* = "FBExtension";
      
      public static const FaqExtension:* = "FaqExtension";
      
      public static const DragonPowerExtension:* = "DragonPowerExtension";
      
      public static const MailBoxExtension:* = "MailBoxExtension";
      
      public static const GiftBagExtension:* = "GiftBagExtension";
      
      public static const ConnectTestExtension:* = "ConnectTestExtension";
      
      public static const BuddyInviteExtension:* = "BuddyInviteExtension";
      
      public static const WeeklyTaskExtension:* = "WeeklyTaskExtension";
      
      public static const PVPExtension:* = "PVPExtension";
      
      public static const StoreExtension:* = "StoreExtension";
      
      public static const FunCardExtension:* = "FunCardExtension";
      
      public static const FestivalExtension:* = "FestivalExtension";
      
      public static const SmallGameExtension:* = "SmallGameExtension";
      
      public static const PVEExtension:* = "PVEExtension";
      
      public static const TeamExtension:* = "TeamExtension";
      
      public static const RideExtension:* = "RideExtension";
      
      public static const PetExtension:* = "PetExtension";
      
      public static const CorpsExtension:* = "CorpsExtension";
      
      public static const TransfiExtension:* = "TransfiExtension";
      
      public static const MemberExtension:* = "MemberExtension";
      
      public static const MemberInviteExtension:* = "MemberInviteExtension";
      
      public static const UsercardExtension:* = "UsercardExtension";
      
      public static const DictionaryExtension:* = "DictionaryExtension";
      
      public static const MultiplayerMeleeExtension:* = "MultiplayerMeleeExtension";
      
      public static const DragonPrayExtension:* = "DragonPrayExtension";
      
      public static const SendFlowersExtension:* = "SendFlowersExtension";
      
      public static const RankExtension:* = "RankExtension";
      
      public static const FamilyCompeteExtension:* = "FamilyCompeteExtension";
      
      public static const TVTExtension:* = "TVTExtension";
      
      public static const TVTDataExtension:* = "TVTDataExtension";
      
      public static const FamilyExtension:* = "FamilyExtension";
      
      public static const BuddyExtension:* = "BuddyExtension";
      
      public static const ClothShowExtension:* = "ClothShowExtension";
      
      public static const PetMusicombatExtension:* = "PetMusicombatExtension";
      
      public static const EMailExtension:* = "EMailExtension";
      
      public static const ExpTreePlanting:* = "ExpTreePlanting";
      
      public static const Achievement:* = "Achievement";
      
      public static const QuestionBank:* = "QuestionBank";
      
      public static const GlobalMatch:* = "GlobalMatch";
      
      public static const PbTVT:* = "PbTVT";
      
      public static const TeacherSystem:* = "TeacherSystem";
      
      public static const AUCTION:* = "Auction";
      
      public static const PbPveExtension:String = "PbPveExtension";
      
      public static const PvpWithSpectatorsExtension:String = "PvpWithSpectatorsExtension";
      
      public static const BountyHunterExtension:String = "BountyHunterExtension";
      
      public static const RoadToLevelUpExtension:* = "RoadToLevelUpExtension";
      
      public static const ArmyExtention:* = "ArmyExtention";
      
      public static const BuyBuffExtension:String = "BuyBuffExtension";
      
      public static const SkyCompetitionExtension:String = "SkyCompetitionExtension";
      
      public static const NoviceExtension:* = "NoviceExtension";
      
      public static const PanelRecordExtension:* = "PanelRecordExtension";
      
      public static const HarbourWarExtension:* = "HarbourWarExtension";
      
      public static const HonorExtension:* = "HonorExtension";
      
      public static const ArenaExtension:String = "ArenaExtension";
      
      public static const ChatRoomExtension:String = "ChatRoomExtension";
      
      public static const EquipLineExtension:String = "EquipLineExtension";
      
      public static const CardExtension:String = "CardExtension";
      
      public static const AssistantBattleExtension:String = "AssistantBattleExtension";
      
      public static const ContractExtension:String = "ContractExtension";
      
      public static const AdventureGroupExtension:String = "AdventureGroupExtension";
      
      public static const LittleStewardExtension:String = "LittleStewardExtension";
      
      public static const TransformExtension:String = "TransformExtension";
      
      public static const ArenaCaptureFlagGameExtension:String = "ArenaCaptureFlagGameExtension";
      
      public static const BattleTeamExtension:String = "BattleTeamExtension";
      
      public static const QuestionnaireExtension:String = "QuestionnaireExtension";
      
      public static const ArmyTeamCompetitionExtension:* = "ArmyTeamCompetitionExtension";
      
      public static const SocialityExtension:String = "SocialityExtension";
      
      public static const CorpsFbExtension:String = "CorpsFbExtension";
      
      public static const NewArenaExtension:String = "NewArenaExtension";
      
      public static const TeamFbExtension:String = "TeamFbExtension";
      
      public static const LingShouDianExtension:String = "LingShouDianExtension";
      
      public static const MagicArenaExtension:String = "MagicArenaExtension";
      
      public static const GodArmorExtension:String = "GodArmorExtension";
      
      public static const FightForRoyaltyExtension:String = "FightForRoyaltyExtension";
      
      public static const EvilTigerExtension:String = "EvilTigerExtension";
      
      public static const TreasureSearchExt:String = "TreasureSearchExt";
      
      public static const FinalEveExtension:String = "FinalEveExtension";
      
      public static const CommonBattleRoomExtension:String = "CommonBattleRoomExtension";
      
      public static const KillBlackDragonExtension:String = "KillBlackDragonExtension";
      
      public static const BraveBattleDragonExtension:String = "BraveBattleDragonExtension";
      
      public static const AnnualFBExtension:String = "AnnualFBExtension";
      
      public static const HotPlainExtension:String = "HotPlainExtension";
      
      public static const NewQuestionnairExtension:String = "NewQuestionnairExtension";
      
      public static const ModifyNickNameExtension:String = "ModifyNickNameExtension";
      
      public static const FlyWingExtension:String = "FlyWingExtension";
      
      public static const ManorExtension:String = "ManorExtension";
      
      public static const FlyPetExtension:String = "FlyPetExtension";
      
      public static const HouseExtension:String = "HouseExtension";
      
      public static const CampExtension:String = "CampExtension";
      
      public static const CamptournamentExtension:String = "CamptournamentExtension";
      
      public static const PraiseFeedbackExtension:String = "PraiseFeedbackExtension";
      
      public static const SuperElementExtension:String = "SuperElementExtension";
      
      public function ExtMap()
      {
         super();
      }
      
      public static function getExtMap() : Object
      {
         if(em == null)
         {
            init();
         }
         return em;
      }
      
      private static function init() : void
      {
         em = new Object();
         em["StartupExtension"] = 1;
         em["UserExtension"] = 2;
         em["BuddyManagerExtension"] = 3;
         em["Log"] = 4;
         em["SurveyExtension"] = 5;
         em["AntiAddictionExtension"] = 6;
         em["PJKExtension"] = 7;
         em["MaterialExtension"] = 8;
         em["SyncExtension"] = 9;
         em["TaskExtension"] = 10;
         em["CustomExtension"] = 11;
         em["MapExtension"] = 12;
         em["TitleExtension"] = 13;
         em["MsgManagerExtension"] = 14;
         em["SimulatorExtension"] = 15;
         em["FBExtension"] = 16;
         em["FaqExtension"] = 17;
         em["DragonPowerExtension"] = 90;
         em["MailBoxExtension"] = 19;
         em["GiftBagExtension"] = 20;
         em["ConnectTestExtension"] = 21;
         em["BuddyInviteExtension"] = 22;
         em["WeeklyTaskExtension"] = 23;
         em["PVPExtension"] = 24;
         em["StoreExtension"] = 25;
         em["FunCardExtension"] = 26;
         em["FestivalExtension"] = 27;
         em["SmallGameExtension"] = 28;
         em["PVEExtension"] = 29;
         em["TeamExtension"] = 30;
         em["RideExtension"] = 31;
         em["PetExtension"] = 32;
         em["CorpsExtension"] = 33;
         em["TransfiExtension"] = 34;
         em["MemberExtension"] = 35;
         em["MemberInviteExtension"] = 36;
         em["UsercardExtension"] = 37;
         em["DictionaryExtension"] = 38;
         em["MultiplayerMeleeExtension"] = 39;
         em["DragonPrayExtension"] = 40;
         em["SendFlowersExtension"] = 41;
         em["RankExtension"] = 42;
         em["FamilyCompeteExtension"] = 44;
         em["TVTExtension"] = 47;
         em["TVTDataExtension"] = 48;
         em["ClothShowExtension"] = 49;
         em["FamilyExtension"] = 43;
         em["PetMusicombatExtension"] = 50;
         em["EMailExtension"] = 51;
         em["ExpTreePlanting"] = 52;
         em["Achievement"] = 53;
         em["QuestionBank"] = 54;
         em["GlobalMatch"] = 56;
         em["PbTVT"] = 57;
         em["BuddyExtension"] = 1000;
         em["TeacherSystem"] = 58;
         em["Auction"] = 59;
         em["PbPveExtension"] = 60;
         em["PvpWithSpectatorsExtension"] = 61;
         em["BountyHunterExtension"] = 62;
         em["RoadToLevelUpExtension"] = 63;
         em["ArmyExtention"] = 65;
         em["BuyBuffExtension"] = 66;
         em["SkyCompetitionExtension"] = 67;
         em["NoviceExtension"] = 68;
         em["PanelRecordExtension"] = 69;
         em["HarbourWarExtension"] = 70;
         em["HonorExtension"] = 71;
         em["CommonBattleRoomExtension"] = 72;
         em["ArenaExtension"] = 73;
         em["ChatRoomExtension"] = 74;
         em["EquipLineExtension"] = 75;
         em["CardExtension"] = 76;
         em["AssistantBattleExtension"] = 77;
         em["ContractExtension"] = 78;
         em["ArenaCaptureFlagGameExtension"] = 84;
         em["AdventureGroupExtension"] = 79;
         em["LittleStewardExtension"] = 81;
         em["QuestionnaireExtension"] = 82;
         em["TransformExtension"] = 83;
         em["BattleTeamExtension"] = 85;
         em["ArmyTeamCompetitionExtension"] = 86;
         em["SocialityExtension"] = 87;
         em["SpeedTestExtension"] = 1001;
         em["CorpsFbExtension"] = 65;
         em["NewArenaExtension"] = 88;
         em["TeamFbExtension"] = 89;
         em["LingShouDianExtension"] = 91;
         em["MagicArenaExtension"] = 92;
         em["GodArmorExtension"] = 93;
         em["FightForRoyaltyExtension"] = 94;
         em["EvilTigerExtension"] = 95;
         em["TreasureSearchExt"] = 96;
         em["FinalEveExtension"] = 97;
         em["KillBlackDragonExtension"] = 98;
         em["BraveBattleDragonExtension"] = 99;
         em["AnnualFBExtension"] = 100;
         em["HotPlainExtension"] = 101;
         em["NewQuestionnairExtension"] = 102;
         em["ModifyNickNameExtension"] = 103;
         em["FlyWingExtension"] = 104;
         em["ManorExtension"] = 105;
         em["FlyPetExtension"] = 106;
         em["HouseExtension"] = 107;
         em["CampExtension"] = 108;
         em["CamptournamentExtension"] = 110;
         em["PraiseFeedbackExtension"] = 111;
         em["SuperElementExtension"] = 112;
      }
   }
}

