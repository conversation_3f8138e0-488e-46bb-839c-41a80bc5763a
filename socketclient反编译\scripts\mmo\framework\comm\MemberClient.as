package mmo.framework.comm
{
   public class MemberClient
   {
      private static var _instance:MemberClient;
      
      private static var extensionName:* = "MemberExtension";
      
      private static const cmdGetPrivileges:* = "11_1";
      
      public static const onGetPrivileges:* = "11_1r";
      
      private static const cmdGetUserMemberType:* = "11_2";
      
      public static const onGetUserMemberType:* = "11_2r";
      
      private static const cmdGetUserServices:* = "11_3";
      
      public static const onGetUserServices:* = "11_3r";
      
      private static const cmdGetUserExpenditrues:* = "11_4";
      
      public static const onGetUserExpenditrues:* = "11_4r";
      
      private static const cmdGetUsersMemberType:* = "11_5";
      
      public static const onGetUsersMemberType:* = "11_5r";
      
      private static const cmdGetUserBalance:* = "11_6";
      
      public static const onGetUserBalance:* = "11_6r";
      
      public function MemberClient()
      {
         super();
      }
      
      public static function get instance() : MemberClient
      {
         if(_instance == null)
         {
            _instance = new MemberClient();
         }
         return _instance;
      }
      
      private function sendXtMessage(param1:String, param2:Object) : *
      {
         SocketClient.instance.sendXtMessage(extensionName,param1,param2);
      }
      
      public function getPrivileges() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdGetPrivileges,_loc1_);
      }
      
      public function getMemberTypeByUserId(param1:int) : *
      {
         var _loc2_:Object = {"userId":param1};
         this.sendXtMessage(cmdGetUserMemberType,_loc2_);
      }
      
      public function getMemberTypeByUserName(param1:String) : *
      {
         var _loc2_:Object = {"userName":param1};
         this.sendXtMessage(cmdGetUserMemberType,_loc2_);
      }
      
      public function getUserServices() : void
      {
         var _loc1_:Object = {};
         this.sendXtMessage(cmdGetUserServices,_loc1_);
      }
      
      public function getUserExpenditrues(param1:int, param2:int, param3:Boolean = false) : void
      {
         var _loc4_:Object = {
            "pageIndex":param1,
            "pageSize":param2,
            "getCount":param3
         };
         this.sendXtMessage(cmdGetUserExpenditrues,_loc4_);
      }
      
      public function getUsersMemberType(param1:String) : *
      {
         var _loc2_:Object = {"uList":param1};
         this.sendXtMessage(cmdGetUsersMemberType,_loc2_);
      }
      
      public function getUserBalance() : *
      {
         this.sendXtMessage(cmdGetUserBalance,null);
      }
   }
}

