package mmo.framework.comm
{
   import flash.system.Capabilities;
   
   public class PixelFlashVerClient
   {
      private static const CMD_RECORD:String = "25_1073_2";
      
      public function PixelFlashVerClient()
      {
         super();
      }
      
      public static function record() : void
      {
         sendRecord(getFlashVersion(),getPixel());
      }
      
      private static function getFlashVersion() : String
      {
         return Capabilities.version;
      }
      
      private static function getPixel() : String
      {
         return Capabilities.screenResolutionX + "x" + Capabilities.screenResolutionY;
      }
      
      private static function sendRecord(param1:String, param2:String) : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.SmallGameExtension,CMD_RECORD,{
            "version":param1,
            "pixel":param2
         });
      }
   }
}

