package mmo.framework.comm
{
   public class PlayerClient
   {
      private static var _instance:PlayerClient;
      
      private static var extensionName:String = "PJKExtension";
      
      private static const RQST_LOAD_CHARACTER_LIST:* = 1101;
      
      private static const RQST_LOAD_CHARACTER:* = 1111;
      
      private static const RQST_VIEW_CHARACTER:* = 1131;
      
      public static const RQST_VIEW_OTHER_CHARACTER:* = "8_1_0";
      
      public static const RQST_VIEW_MY_DATA:* = "8_1_1";
      
      public static const RQST_VIEW_ALL_AVATAR:* = "8_1_4";
      
      public static const RSPN_LOAD_CHARACTER_LIST:* = 2101;
      
      public static const RSPN_LOAD_CHARACTER:* = 2111;
      
      public static const RSPN_VIEW_CHARACTER:* = 2131;
      
      public static const RSPN_VIEW_OTHER_CHARACTER:* = "8_1_0";
      
      public static const RSPN_VIEW_MY_DATA:* = "8_1_1";
      
      public static const RSPN_AFTER_VIEW_EQUIP:* = "rspn_after_view_equip";
      
      public static const RSPN_FIGHTING_STATUS_CHANGED:* = 2421;
      
      public static const RSPN_HANDLE_MPHP_UPDATE:* = "rspn_handle_mphp_update";
      
      public static const RSPN_GAIN_EXPERIENCE:* = 2141;
      
      public static const RSPN_LEVEL_UP:* = 2143;
      
      public static const RSPN_LEVEL_UP_OTHER:* = 2144;
      
      public function PlayerClient()
      {
         super();
      }
      
      public static function getInstance() : PlayerClient
      {
         if(_instance == null)
         {
            _instance = new PlayerClient();
         }
         return _instance;
      }
      
      public function loadCharacterList() : void
      {
         this.sendXtMessage(RQST_LOAD_CHARACTER_LIST,null);
      }
      
      public function loadCharacter(param1:int) : void
      {
         var _loc2_:Object = {"ci":param1};
         this.sendXtMessage(RQST_LOAD_CHARACTER,_loc2_);
      }
      
      public function viewOtherCharacter(param1:Object) : void
      {
         if(param1 is int)
         {
            SocketClient.instance.sendXtMessage("MaterialExtension",RQST_VIEW_OTHER_CHARACTER,{"charId":param1});
         }
         else if(param1 is String)
         {
            SocketClient.instance.sendXtMessage("MaterialExtension",RQST_VIEW_OTHER_CHARACTER,{"nick":param1});
         }
      }
      
      public function viewCharacterAvatar(param1:int) : void
      {
         SocketClient.instance.sendXtMessage("MaterialExtension",RQST_VIEW_ALL_AVATAR,{"cid":param1});
      }
      
      public function viewMyData() : void
      {
         SocketClient.instance.sendXtMessage("MaterialExtension",RQST_VIEW_MY_DATA,{});
      }
      
      public function viewCharacter(param1:int) : void
      {
         var _loc2_:Object = {"t":param1};
         this.sendXtMessage(RQST_VIEW_CHARACTER,_loc2_);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : *
      {
         SocketClient.instance.sendXtMessage(extensionName,param1,param2);
      }
   }
}

