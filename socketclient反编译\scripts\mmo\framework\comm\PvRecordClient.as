package mmo.framework.comm
{
   import com.adobe.utils.StringUtil;
   import com.hexagonstar.util.debug.Debug;
   import mmo.common.JsProxy;
   import mmo.common.shareobject.LocalStore;
   import mmo.common.shareobject.ShareObjectManager2;
   
   public class PvRecordClient
   {
      public static const Machine_Old:int = 0;
      
      public static const Machine_New:int = 1;
      
      public static const instance:PvRecordClient = new PvRecordClient();
      
      private static const CMD_RECORD:String = "25_1073_1";
      
      private static const KEY_MACHINE_STATE:String = "KEY_MACHINE_STATE";
      
      private var machineState:Boolean = false;
      
      public function PvRecordClient()
      {
         super();
      }
      
      private static function getUrl() : String
      {
         var _loc1_:String = getDomainUrl(JsProxy.getReferer());
         if(_loc1_ == null || StringUtil.trim(_loc1_) == "")
         {
            Debug.trace("url null");
            return null;
         }
         Debug.trace(_loc1_);
         return _loc1_;
      }
      
      private static function getDomainUrl(param1:String) : String
      {
         var _loc2_:* = param1.split("/");
         if(_loc2_.length == 1)
         {
            return param1;
         }
         return _loc2_[2];
      }
      
      private static function isOldMachine() : Boolean
      {
         var _loc1_:Vector.<Object> = null;
         _loc1_ = ShareObjectManager2.instance.getAllLocalUserInfo();
         var _loc2_:Boolean = _loc1_ != null && _loc1_.length > 0;
         var _loc3_:Boolean = Boolean(LocalStore.getValue(KEY_MACHINE_STATE));
         return _loc2_ || _loc3_;
      }
      
      private static function sendRecord(param1:String, param2:int) : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.SmallGameExtension,CMD_RECORD,{
            "url":param1,
            "machine":param2
         });
      }
      
      public function init() : void
      {
         this.machineState = isOldMachine();
         LocalStore.setValue(KEY_MACHINE_STATE,true);
      }
      
      public function record() : void
      {
         var _loc1_:String = getUrl();
         if(_loc1_ == null)
         {
            return;
         }
         sendRecord(_loc1_,this.machineState ? Machine_Old : Machine_New);
      }
   }
}

