package mmo.framework.comm
{
   public class SceneObjectClient
   {
      public static var _instance:SceneObjectClient;
      
      private static var extensionName:String = "PJKExtension";
      
      private static const RQST_GAIN_SCENE_OBJECT:* = "1315";
      
      public static const RSPN_OBJECT_LIST:* = 2311;
      
      public static const RSPN_NEW_OBJECT:* = 2312;
      
      public static const RSPN_REMOVE_OBJECT:* = 2313;
      
      public static const RSPN_DESTORY_OBJECT:* = 2314;
      
      public static const RSPN_GAIN_OBJECT:* = 2315;
      
      public static const RSPN_BATTLE_FIELD_GAIN_ITEM_FOR_SPECIAL:* = 2316;
      
      public static const RSPN_ITEM_ACTION:* = 2472;
      
      public function SceneObjectClient()
      {
         super();
      }
      
      public static function get instance() : SceneObjectClient
      {
         if(_instance == null)
         {
            _instance = new SceneObjectClient();
         }
         return _instance;
      }
      
      public function gainSceneObject(param1:int) : void
      {
         var _loc2_:Object = {"ii":param1};
         this.sendXtMessage(RQST_GAIN_SCENE_OBJECT,_loc2_);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : *
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(extensionName,param1,param2);
      }
   }
}

