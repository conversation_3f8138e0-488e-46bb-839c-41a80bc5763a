package mmo.framework.comm
{
   import flash.utils.ByteArray;
   import mmo.common.eventdispatcher.CommonEvent;
   import mmo.common.eventdispatcher.CommonEventDispatcher;
   
   public class SkillHitClient
   {
      private static var _instance:SkillHitClient;
      
      private static var extensionName:String = ExtMap.PJKExtension;
      
      private static const MAX_TARGET_LENGTH:Number = 16;
      
      public static const RQST_USE_SKILL:* = 11;
      
      public static const RQST_PET_USE_SKILL:* = 12;
      
      public static const RQST_CREEP_USE_SKILL:* = 13;
      
      public static const RQST_BATTLE_FIELD_USE_SKILL:* = 14;
      
      public static const BCMD_RQST_STRUCTURE_USE_SKILL:* = 15;
      
      public static const BCMD_RQST_ITEM_USE_SKILL:* = 16;
      
      public static const RSPN_SKILL_HIT:* = 2411;
      
      public static const RSPN_CREEP_CAPTRUE:* = 2461;
      
      public function SkillHitClient()
      {
         super();
      }
      
      public static function get instance() : SkillHitClient
      {
         if(_instance == null)
         {
            _instance = new SkillHitClient();
         }
         return _instance;
      }
      
      public function monsterSkillHit(param1:int, param2:Number, param3:Number, param4:uint) : void
      {
         this._monsterSkillHit(param1,param2,param4,[param3]);
      }
      
      public function monsterSkillHits(param1:int, param2:Number, param3:Array, param4:uint) : void
      {
         this._monsterSkillHit(param1,param2,param4,param3);
      }
      
      public function petSkillHit(param1:int, param2:Number, param3:Number, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,[param3],0);
      }
      
      public function petSkillHits(param1:int, param2:Number, param3:Array, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,param3,0);
      }
      
      public function summonPetSkillHit(param1:int, param2:Number, param3:Number, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,[param3],1);
      }
      
      public function summonPetSkillHits(param1:int, param2:Number, param3:Array, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,param3,1);
      }
      
      public function flyPetSkillHit(param1:int, param2:Number, param3:Number, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,[param3],2);
      }
      
      public function flyPetSkillHits(param1:int, param2:Number, param3:Array, param4:uint) : void
      {
         this._petSkillHit(param1,param2,param4,param3,2);
      }
      
      public function characterSkillHit(param1:int, param2:Number, param3:uint) : void
      {
         this._characterSkillHits(param1,[param2],param3);
      }
      
      public function characterSkillHits(param1:int, param2:Array, param3:uint) : void
      {
         var _loc4_:int = Math.ceil(param2.length / MAX_TARGET_LENGTH);
         var _loc5_:int = 0;
         while(_loc5_ < _loc4_)
         {
            this._characterSkillHits(param1,param2.slice(_loc5_ * MAX_TARGET_LENGTH,_loc5_ * MAX_TARGET_LENGTH + MAX_TARGET_LENGTH),param3);
            _loc5_++;
         }
      }
      
      public function trapHit(param1:int, param2:uint, param3:Array = null) : void
      {
         this._trapSkillHit(param1,param2,param3);
      }
      
      public function towerHit(param1:int, param2:uint, param3:Array = null) : void
      {
         this._towerSkillHit(param1,param2,param3);
      }
      
      public function machineHit(param1:int, param2:int, param3:uint, param4:Array = null) : void
      {
         this._machineSkillHit(param1,param2,param3,param4);
      }
      
      public function sceneObjectHitByCreep(param1:int, param2:Number, param3:uint, param4:Number) : void
      {
         this._monsterSkillHit(param1,param2,param3,[param4]);
      }
      
      private function _characterSkillHits(param1:int, param2:Array, param3:uint) : void
      {
         var _loc4_:Object = {
            "s":param1,
            "t":param2,
            "d":param3
         };
         CommonEventDispatcher.instance.dispatchEvent(new CommonEvent("catch_me_If_you_can",_loc4_));
         SocketManager.instance.activeSocketClient.sendXtMessageByte(RQST_USE_SKILL,_loc4_.p);
      }
      
      private function _trapSkillHit(param1:int, param2:uint, param3:Array = null) : void
      {
         var _loc5_:int = 0;
         var _loc4_:ByteArray = new ByteArray();
         _loc4_.writeInt(param1);
         if(param3 == null)
         {
            _loc4_.writeByte((param2 << 4) + 0);
         }
         else
         {
            _loc4_.writeByte((param2 << 4) + param3.length);
            for each(_loc5_ in param3)
            {
               _loc4_.writeInt(_loc5_);
            }
         }
         SocketManager.instance.activeSocketClient.sendXtMessageByte(RQST_BATTLE_FIELD_USE_SKILL,_loc4_);
      }
      
      private function _towerSkillHit(param1:int, param2:uint, param3:Array = null) : void
      {
         var _loc5_:int = 0;
         var _loc4_:ByteArray = new ByteArray();
         _loc4_.writeInt(param1);
         _loc4_.writeInt(param3.length);
         for each(_loc5_ in param3)
         {
            _loc4_.writeInt(_loc5_);
         }
         SocketManager.instance.activeSocketClient.sendXtMessageByte(BCMD_RQST_STRUCTURE_USE_SKILL,_loc4_);
      }
      
      private function _machineSkillHit(param1:int, param2:int, param3:uint, param4:Array = null) : void
      {
         var _loc6_:int = 0;
         var _loc5_:ByteArray = new ByteArray();
         _loc5_.writeInt(param1);
         _loc5_.writeInt(param2);
         _loc5_.writeInt(param4.length);
         for each(_loc6_ in param4)
         {
            _loc5_.writeInt(_loc6_);
         }
         SocketManager.instance.activeSocketClient.sendXtMessageByte(BCMD_RQST_ITEM_USE_SKILL,_loc5_);
      }
      
      private function _monsterSkillHit(param1:int, param2:Number, param3:uint, param4:Array) : void
      {
         var _loc5_:Object = {
            "s":param1,
            "m":param2,
            "d":param3,
            "tl":param4
         };
         CommonEventDispatcher.instance.dispatchEvent(new CommonEvent("catch_me_if_you_can",_loc5_));
         SocketManager.instance.activeSocketClient.sendXtMessageByte(RQST_CREEP_USE_SKILL,_loc5_.p);
      }
      
      private function _petSkillHit(param1:int, param2:Number, param3:uint, param4:Array, param5:int) : void
      {
         var _loc7_:int = 0;
         var _loc6_:ByteArray = new ByteArray();
         _loc6_.writeInt(param1);
         _loc6_.writeInt(param2);
         _loc6_.writeByte(param5);
         _loc6_.writeByte((param3 << 4) + param4.length);
         for each(_loc7_ in param4)
         {
            _loc6_.writeInt(_loc7_);
         }
         SocketManager.instance.activeSocketClient.sendXtMessageByte(RQST_PET_USE_SKILL,_loc6_);
      }
   }
}

