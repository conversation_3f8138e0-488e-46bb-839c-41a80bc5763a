package mmo.framework.comm
{
   import flash.events.EventDispatcher;
   import flash.utils.ByteArray;
   import mmo.ServerClient;
   import mmo.config.ConfigReader;
   import mmo.framework.comm.handler.CmdHandlerMap;
   import mmo.framework.comm.listener.RoomEventListener;
   import mmo.framework.comm.listener.SysEventListener;
   import mmo.framework.comm.listener.UserVarListener;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.data.Room;
   import mmo.socketserver.data.User;
   
   public class SocketClient extends EventDispatcher
   {
      private static var _instance:SocketClient;
      
      private var _socketServerClient:SocketServerClient;
      
      private var cmdHandlers:Object;
      
      private var _userVarListener:UserVarListener;
      
      private var _roomEventListener:RoomEventListener;
      
      private var _sysEventListener:SysEventListener;
      
      public function SocketClient()
      {
         super();
         this.initCommands();
      }
      
      public static function get instance() : SocketClient
      {
         if(_instance == null)
         {
            _instance = new SocketClient();
         }
         return _instance;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         SocketManager.instance.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         SocketManager.instance.removeEventListener(param1,param2,param3);
      }
      
      internal function addEventListenerInner(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      internal function removeEventListenerInner(param1:String, param2:Function, param3:Boolean = false) : void
      {
         super.removeEventListener(param1,param2,param3);
      }
      
      public function resetSocket(param1:SocketServerClient) : void
      {
         if(this._socketServerClient != null)
         {
            this.setAllEventListeners(false);
         }
         this._socketServerClient = param1;
         if(Boolean(this._userVarListener))
         {
            this._userVarListener.enable = false;
         }
         this._userVarListener = new UserVarListener(this.socketClient,this);
         if(Boolean(this._roomEventListener))
         {
            this._roomEventListener.enable = false;
         }
         this._roomEventListener = new RoomEventListener(this.socketClient,this);
         if(Boolean(this._sysEventListener))
         {
            this._sysEventListener.enable = false;
         }
         this._sysEventListener = new SysEventListener(this.socketClient,this,this.cmdHandlers);
         this.setAllEventListeners(true);
      }
      
      public function disconnect() : void
      {
         if(this._socketServerClient != null && Boolean(this._socketServerClient.isConnected))
         {
            if(this._socketServerClient.myUserName != "")
            {
               this._socketServerClient.disconnect();
            }
         }
      }
      
      public function setUserGameRoom(param1:String, param2:int, param3:int, param4:Boolean = true) : void
      {
         var _loc5_:Object = {
            "gameRoom":param1,
            "gameNum":param2,
            "gameIndex":param3,
            "isTempRoom":param4
         };
         this.socketClient.setUserVariables(_loc5_);
      }
      
      public function setGamePlayerCount(param1:String, param2:int) : void
      {
         var _loc3_:Array = new Array();
         _loc3_.push({
            "name":param1,
            "val":param2,
            "persistent":true
         });
         this.socketClient.setRoomVariables(_loc3_,this.socketClient.activeRoomId,false);
      }
      
      public function setUserVariables(param1:Object, param2:int = -1, param3:Boolean = false) : void
      {
         var _loc6_:String = null;
         var _loc7_:* = undefined;
         var _loc8_:SSEvent = null;
         var _loc4_:Object = {};
         var _loc5_:User = this.currentUser;
         _loc4_.user = _loc5_;
         _loc4_.changedVars = new Array();
         if(param3)
         {
            for(_loc6_ in param1)
            {
               _loc7_ = param1[_loc6_];
               if(param1[_loc6_] != _loc5_.getVariable(_loc6_))
               {
                  _loc4_.changedVars.push(param1[_loc6_]);
                  _loc4_.changedVars[_loc6_] = true;
               }
            }
         }
         this.socketClient.setUserVariables(param1,param2);
         if(param3)
         {
            _loc8_ = new SSEvent(SSEvent.onUserVariablesUpdate,_loc4_);
            this.socketClient.dispatchEvent(_loc8_);
         }
      }
      
      public function isIdle() : Boolean
      {
         return this.socketClient.isIdle(ConfigReader.instance.idleTime);
      }
      
      public function hit() : void
      {
         this.socketClient.hit();
      }
      
      private function get socketClient() : SocketServerClient
      {
         return this._socketServerClient;
      }
      
      public function get isConnected() : Boolean
      {
         return this.socketClient.isConnected;
      }
      
      public function get myUserId() : int
      {
         return this.socketClient.myUserId;
      }
      
      public function get myUserName() : String
      {
         return this.socketClient.myUserName;
      }
      
      public function get firstLogin() : Boolean
      {
         return ServerClient.instance.lastLoginTime < 0;
      }
      
      public function get lastLoginTime() : Number
      {
         return ServerClient.instance.lastLoginTime;
      }
      
      public function get currentUser() : User
      {
         return this.activeRoom.getUser(this.myUserId);
      }
      
      public function get activeRoomId() : int
      {
         return this.socketClient.activeRoomId;
      }
      
      public function get activeRoom() : Room
      {
         return this.socketClient.getActiveRoom();
      }
      
      public function get ipAddress() : String
      {
         return this._socketServerClient.ipAddress;
      }
      
      public function get port() : int
      {
         return this._socketServerClient.port;
      }
      
      public function get zoneName() : String
      {
         return this._socketServerClient.zoneName;
      }
      
      private function initCommands() : void
      {
         this.cmdHandlers = CmdHandlerMap.getHandlerMap(this);
      }
      
      private function setAllEventListeners(param1:Boolean) : void
      {
         this._roomEventListener.enable = param1;
         this._userVarListener.enable = param1;
         this._sysEventListener.enable = param1;
      }
      
      public function sendCustomCommand(param1:String, param2:Object) : void
      {
         param2.customCmd = param1;
         param2.cmd = Commands.customCommand;
         this.socketClient.sendObject(param2,this.activeRoomId);
      }
      
      public function sendXtMessage(param1:String, param2:String, param3:*, param4:String = "xml", param5:int = -1) : void
      {
         var _loc6_:int = int(ExtMap.getExtMap()[param1]);
         if(_loc6_ <= 0)
         {
            throw new Error("Cannot find extension name:" + param1);
         }
         if(this.socketClient.isConnected)
         {
            this.socketClient.sendXtMessage(_loc6_,param2,param3,param4,param5);
         }
      }
      
      public function sendXtMsgWithCallBack(param1:String, param2:String, param3:*, param4:Function = null, param5:Boolean = true) : void
      {
         var xtName:String = param1;
         var cmd:String = param2;
         var params:* = param3;
         var next:Function = param4;
         var lock:Boolean = param5;
         SocketClient.instance.addEventListener(cmd,function(param1:SocketClientEvent):void
         {
            SocketClient.instance.removeEventListener(cmd,arguments.callee);
            if(next != null)
            {
               next.apply(null,[param1]);
            }
         });
         SocketClient.instance.sendXtMessage(xtName,cmd,params);
      }
      
      public function sendXtMessageByte(param1:int, param2:ByteArray, param3:int = -1) : void
      {
         if(this.socketClient.isConnected)
         {
            this.socketClient.sendXtMessageByte(param1,param2,param3);
         }
      }
      
      public function joinRoom(param1:*, param2:String = "", param3:Boolean = false, param4:Boolean = false, param5:Boolean = false, param6:int = -1) : void
      {
         this.socketClient.joinRoom(param1,param2,param3,param4,param5,param6);
      }
   }
}

