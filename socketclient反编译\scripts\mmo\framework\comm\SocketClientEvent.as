package mmo.framework.comm
{
   import flash.events.Event;
   
   public class SocketClientEvent extends Event
   {
      public static const onInitCompleted:* = "onInitCompleted";
      
      public static const onCustomCommand:* = "onCustomCommand";
      
      public static const onUserVariablesUpdate:String = "onUserVariablesUpdate";
      
      public static const onRoomVariablesUpdate:String = "onRoomVariablesUpdate";
      
      public static const onAdminMessage:String = "onAdminMessage";
      
      public static const onExtensionResponse:String = "onExtensionResponse";
      
      public static const onConnectionLost:String = "onConnectionLost";
      
      public static const onRoomListUpdate:String = "onRoomListUpdate";
      
      public static const onJoinRoom:String = "onJoinRoom";
      
      public static const onBuddyList:String = "onBuddyList";
      
      public static const onBuddyListUpdate:String = "onBuddyListUpdate";
      
      public static const onBuddyOnline:String = "onBuddyOnline";
      
      public static const onBuddyListError:String = "onBuddyListError";
      
      public static const onBuddyPermissionRequest:String = "onBuddyPermissionRequest";
      
      public static const onBlockList:String = "onBlockList";
      
      public static const onBlockListError:String = "onBlockListError";
      
      public static const GET_BUDDY_HOUSES_UPDATE_DATE:String = "cmdGetBuddyHousesUpdateDate";
      
      public static const UPDATE_BUDDY_GROUP:String = "cmdUpdateBuddyGroup";
      
      public static const GET_BUDDY_GROUP:String = "cmdGetBuddyGroup";
      
      public static const onUserEnterRoom:String = "onUserEnterRoom";
      
      public static const onUserLeaveRoom:String = "onUserLeaveRoom";
      
      public static const onRoomAdded:String = "onRoomAdded";
      
      public static const onCreateRoomError:String = "onCreateRoomError";
      
      public static const onJoinRoomError:String = "onJoinRoomError";
      
      public static const onUserCountChange:String = "onUserCountChange";
      
      public static const onUserSkillListChange:String = "onUserSkillListChange";
      
      public static const onUserAvatarChange:String = "onUserAvatarChange";
      
      public static const onUserAdditionalClothesChange:String = "onUserAdditionalClothesChange";
      
      public static const onUserEquipChange:String = "onUserEquipChange";
      
      public static const onUserFloatStateChange:String = "onUserFloatStateChange";
      
      public static const onUserRideStateChange:String = "onUserRideStateChange";
      
      public static const onUserFlywingStateChange:String = "onUserFlywingStateChange";
      
      public static const onUserGoldCarriageStateChange:String = "onUserGoldCarriageStateChange";
      
      public static const onUserCampStateChange:String = "onUserCampStateChange";
      
      public static const onFamilySimpleInfoUpdate:String = "onFamilySimpleInfoChange";
      
      public static const onUserArmyInfoUpdate:String = "onArmyInfoUpdate";
      
      public static const onUserEquipSoulUpdate:String = "onUserEquipSoulUpdate";
      
      public static const onFamilyMsg:String = "onFamilyMsg";
      
      public static const onUserMemberStateChange:String = "onUserMemberStateChange";
      
      public static const onMovingStateChanged:String = "onMovingStateChanged";
      
      public static const onPublicMessage:String = "onPublicMessage";
      
      public static const onPrivateMessage:String = "onPrivateMsg";
      
      public static const onInitUserPosition:String = "onInitUserPosition";
      
      public static const onUserJump:String = "onUserJump";
      
      public static const onUserMove:String = "onUserMove";
      
      public static const onUserChangeDirection:String = "onUserChangeDirection";
      
      public static const onGetOfflineMsg:String = "onGetOfflineMsg";
      
      public static const onEmote:String = "onEmote";
      
      public static const onAction:String = "onAction";
      
      public static const GET_LOCATION_BACK:String = "onLocateBuddy";
      
      public static const onDecreaseScore:String = "onDecreaseScore";
      
      public static const onSetUserGameRoom:* = "onSetUserGameRoom";
      
      public static const onLocateBuddy:String = "onLocateBuddy";
      
      public static const getStartInfo:* = "getStartInfo";
      
      public static const getOfflineMsg:* = "getOfflineMsg";
      
      public static const switchInviteTask:* = "switchInviteTask";
      
      public static const onGetWorkState:* = "onGetWorkState";
      
      public static const onAddRandomScore:* = "onAddRandomScore";
      
      public static const onGetGuideState:* = "onGetGuideState";
      
      public static const onRegistGuide:* = "onRegistGuide";
      
      public static const onSubmitGuide:* = "onSubmitGuide";
      
      public static const onGetGuideQuestions:* = "onGetGuideQuestions";
      
      public static const onCheckUserExist:* = "onCheckUserExist";
      
      public static const onIsHighlightToday:* = "isHighlightToday";
      
      public static const onSysMsg:* = "onSysMsg";
      
      public static const onFamilyInvite:* = "familyInvite";
      
      public static const onMyCharJoinBattleField:* = "onMyCharacterJoinBattleField";
      
      public static const onOtherCharJoinBattleField:* = "onOhterCharacterJoinBattleField";
      
      public static const onMyCharLeaveBattleField:* = "onMyCharacterLeaveBattleField";
      
      public static const onOtherCharLeaveBattleField:* = "onOtherCharacterLeaveBattleField";
      
      public static const onMonsterJoinBattleField:* = "onMonsterJoinBattleField";
      
      public static const onMonsterLeaveBattleField:* = "onMonsterLeaveBattleField";
      
      public static const onCharacterTitleUpdate:* = "onTitleUpdate";
      
      public static const onCharacterVectorUpdate:* = "onChacacterVecterUpdate";
      
      public static const onMonsterChangeDirection:* = "onMonsterChangeDirection";
      
      public static const onCharacterJump:* = "onCharacterJump";
      
      public static const onMonsterJump:* = "onMonsterJump";
      
      public static const onCharacterDie:* = "onCharacterDie";
      
      public static const onMyCharacterDie:* = "onMyCharacterDie";
      
      public static const onMonsterDie:* = "onMonsterDie";
      
      public static const onCharacterRevive:* = "onCharacterRevive";
      
      public static const onMonsterRevive:* = "onMonsterRevive";
      
      public static const onMonsterMove:* = "onMonsterMove";
      
      public static const onMonsterStop:* = "onMonsterStop";
      
      public static const onMonsterStand:* = "onMonsterStand";
      
      public static const onFlyPetMove:* = "onFlyPetMove";
      
      public static const onFlyPetStop:* = "onFlyPetStop";
      
      public static const onFlyPetStand:* = "onFlyPetStand";
      
      public static const onFlyPetDie:* = "onFlyPetDie";
      
      public static const onTitleView:* = "14_1";
      
      public static const onTitleSet:* = "14_2";
      
      public static const onTitleGain:* = "14_3";
      
      public static const onTitleDel:* = "14_4";
      
      public static const onUserStateChange:* = "onUserStateChange";
      
      public static const cmdCreateAndJoinRoom:* = "cmdCreateAndJoinRoom";
      
      public var params:Object;
      
      public function SocketClientEvent(param1:String, param2:Object)
      {
         super(param1);
         this.params = param2;
      }
      
      override public function clone() : Event
      {
         return new SocketClientEvent(this.type,this.params);
      }
      
      override public function toString() : String
      {
         return formatToString("SocketClientEvent","type","bubbles","cancelable","eventPhase","params");
      }
   }
}

