package mmo.framework.comm
{
   import flash.external.ExternalInterface;
   import flash.utils.ByteArray;
   import mmo.config.ConfigReader;
   import mmo.framework.comm.login.SocketLoginProxy;
   import mmo.socketserver.MsgSeq;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.data.Room;
   
   public class SocketManager
   {
      private static var _instance:SocketManager;
      
      public static const MAIN_SOCKET_ID:int = 0;
      
      public static const AUX_SOCKET_ID:int = 1;
      
      private var _socketList:Vector.<SocketServerClient> = new Vector.<SocketServerClient>();
      
      private var _clientList:Vector.<SocketClient> = new Vector.<SocketClient>();
      
      private var _debug:Boolean;
      
      private var _activeSocketId:int = 0;
      
      public function SocketManager()
      {
         super();
         this._debug = ConfigReader.instance.socketDebug;
         var _loc1_:SocketServerClient = new SocketServerClient(this._debug,new MsgSeq());
         this._socketList[MAIN_SOCKET_ID] = _loc1_;
         _loc1_ = new SocketServerClient(this._debug,new MsgSeq());
         this._socketList[AUX_SOCKET_ID] = _loc1_;
         var _loc2_:SocketClient = SocketClient.instance;
         var _loc3_:SocketClient = new SocketClient();
         this._clientList[MAIN_SOCKET_ID] = _loc2_;
         this._clientList[AUX_SOCKET_ID] = _loc3_;
      }
      
      public static function get instance() : SocketManager
      {
         if(!_instance)
         {
            _instance = new SocketManager();
         }
         return _instance;
      }
      
      internal function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:SocketClient = null;
         for each(_loc6_ in this._clientList)
         {
            _loc6_.addEventListenerInner(param1,param2,param3,param4,param5);
         }
      }
      
      internal function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc4_:SocketClient = null;
         for each(_loc4_ in this._clientList)
         {
            _loc4_.removeEventListenerInner(param1,param2,param3);
         }
      }
      
      public function get activeSocketId() : int
      {
         return this._activeSocketId;
      }
      
      public function set activeSocketId(param1:int) : void
      {
         this._activeSocketId = param1;
      }
      
      public function get activeRoom() : Room
      {
         var _loc1_:SocketServerClient = this._socketList[this._activeSocketId];
         return _loc1_.activeRoom;
      }
      
      public function get activeSocketClient() : SocketClient
      {
         return this._clientList[this._activeSocketId];
      }
      
      public function getSocketClientByID(param1:int) : SocketClient
      {
         return this._clientList[param1];
      }
      
      public function getAuxSocketClient() : SocketClient
      {
         return this._clientList[AUX_SOCKET_ID];
      }
      
      public function getLoginProxyBySocketId(param1:int) : SocketLoginProxy
      {
         var _loc2_:SocketServerClient = this._socketList[param1];
         return new SocketLoginProxy(_loc2_);
      }
      
      public function resetMainSocketClient() : void
      {
         var _loc1_:SocketServerClient = this._socketList[MAIN_SOCKET_ID];
         SocketClient.instance.resetSocket(_loc1_);
      }
      
      public function resetAuxSocketClient() : void
      {
         var _loc1_:SocketServerClient = this._socketList[AUX_SOCKET_ID];
         var _loc2_:SocketClient = this._clientList[AUX_SOCKET_ID];
         _loc2_.resetSocket(_loc1_);
      }
      
      public function disconnectAuxSocket() : void
      {
         var _loc1_:SocketServerClient = this._socketList[AUX_SOCKET_ID];
         if(_loc1_.isConnected)
         {
            _loc1_.disconnect();
         }
      }
      
      public function addContainerCallback() : void
      {
         try
         {
            ExternalInterface.addCallback("closeSocket",this.disconnectAllSocket);
         }
         catch(e:Error)
         {
         }
      }
      
      public function disconnectAllSocket(param1:String) : void
      {
         var _loc2_:SocketServerClient = null;
         for each(_loc2_ in this._socketList)
         {
            if(Boolean(_loc2_) && Boolean(_loc2_.isConnected))
            {
               _loc2_.disconnect();
            }
         }
      }
      
      public function sendXtMesssageToActiveSocket(param1:String, param2:String, param3:Object) : void
      {
         var _loc4_:SocketClient = this._clientList[this._activeSocketId];
         _loc4_.sendXtMessage(param1,param2,param3);
      }
      
      public function sendXtMessageByteToActiveSocket(param1:int, param2:ByteArray) : void
      {
         var _loc3_:SocketClient = this._clientList[this._activeSocketId];
         _loc3_.sendXtMessageByte(param1,param2);
      }
   }
}

