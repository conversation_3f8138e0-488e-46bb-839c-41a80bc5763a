package mmo.framework.comm
{
   public class TVTDataClient
   {
      private static var _instance:TVTDataClient;
      
      private static const EXTENSION_NAME:String = ExtMap.TVTDataExtension;
      
      public static const CMD_VIEW_RECORD:String = "48_1";
      
      public function TVTDataClient()
      {
         super();
      }
      
      public static function get instance() : TVTDataClient
      {
         if(_instance == null)
         {
            _instance = new TVTDataClient();
         }
         return _instance;
      }
      
      public function viewUserTVTData(param1:int) : void
      {
         this.sendXtMessage(CMD_VIEW_RECORD,{"ta":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

