package mmo.framework.comm
{
   public class UserClient
   {
      private static const EXT_NAME:String = "UserExtension";
      
      public static const GET_START_INFO:String = "getStartInfo";
      
      public static const GET_OFFLINE_MSG:String = "getOfflineMsg";
      
      public static const GET_TIME:String = "getCurrentTime";
      
      public function UserClient()
      {
         super();
      }
      
      public static function getStartInfo() : void
      {
         sendXtMessage(GET_START_INFO,null);
      }
      
      public static function getOfflineMsg() : void
      {
         sendXtMessage(GET_OFFLINE_MSG,{
            "firstLogin":SocketClient.instance.firstLogin,
            "lastLoginTime":SocketClient.instance.lastLoginTime
         });
      }
      
      public static function getTime() : void
      {
         sendXtMessage(GET_TIME,null);
      }
      
      private static function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,param1,param2);
      }
   }
}

