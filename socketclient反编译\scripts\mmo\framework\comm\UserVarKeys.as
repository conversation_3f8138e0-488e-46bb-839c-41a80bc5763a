package mmo.framework.comm
{
   public class UserVarKeys
   {
      public static const SKILL_LIST:String = "skills";
      
      public static const AVATAR:String = "avatar";
      
      public static const EQUIP:String = "equip";
      
      public static const EQUIP_SOUL:String = "soul";
      
      public static const ADDITIONAL_CLOTHES:String = "av";
      
      public static const RIDE_STATUS:String = "ride";
      
      public static const TEAM:String = "t";
      
      public static const VIP_INFO:String = "mi";
      
      public static const ANTI_ADDICTION:String = "float";
      
      public static const FAMILY_INFO:String = "family";
      
      public static const ARMY:String = "army";
      
      public static const MARRY:String = "contract_notice_lb";
      
      public static const VipClothesView:String = "cv";
      
      public static const FLY_WING_KEY:String = "fly_wing";
      
      public static const CAMP_KEY:String = "campvar";
      
      public static const GOLD_CARRAIGE_KEY:String = "gold_carriage";
      
      public function UserVarKeys()
      {
         super();
      }
   }
}

