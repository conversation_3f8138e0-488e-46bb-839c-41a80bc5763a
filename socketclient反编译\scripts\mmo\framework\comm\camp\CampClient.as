package mmo.framework.comm.camp
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class CampClient
   {
      private static const EXTENSION_NAME:String = ExtMap.CampExtension;
      
      private static const CMD_GET_CAMPINFO:String = "108_1";
      
      private static const CMD_JOIN_CAMP:String = "108_2";
      
      public static const CMD_CAMPINFO_CAHGNE:String = "108_3";
      
      private static const CMD_JUANXIAN:String = "108_12";
      
      private static const CMD_XUNLUO:String = "108_11";
      
      private static const CMD_LEVEL_UP_BING:String = "108_20";
      
      public function CampClient()
      {
         super();
      }
      
      public static function getCampInfo(param1:Function = null, param2:Object = null) : void
      {
         sendXtMsgWithCallBack(CMD_GET_CAMPINFO,param1,param2);
      }
      
      public static function joinCamp(param1:Function = null, param2:Object = null) : void
      {
         sendXtMsgWithCallBack(CMD_JOIN_CAMP,param1,param2);
      }
      
      public static function juanxian(param1:Function = null, param2:Object = null) : void
      {
         sendXtMsgWithCallBack(CMD_JUANXIAN,param1,param2);
      }
      
      public static function xunluo(param1:Function = null, param2:Object = null) : void
      {
         sendXtMsgWithCallBack(CMD_XUNLUO,param1,param2);
      }
      
      public static function levelUpBing(param1:Function = null, param2:Object = null) : void
      {
         sendXtMsgWithCallBack(CMD_LEVEL_UP_BING,param1,param2);
      }
      
      private static function sendMsg(param1:String, param2:Object = null) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
      
      private static function sendXtMsgWithCallBack(param1:String, param2:Function = null, param3:Object = null) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,param1,param3,param2);
      }
   }
}

