package mmo.framework.comm.client
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class PVECommonClient
   {
      private static var _instance:PVECommonClient = null;
      
      private static const EXTENSION_NAME:String = ExtMap.PVEExtension;
      
      public static const REQUEST_JOIN_PVE:String = "9_1_0";
      
      public static const REFRESH_MONSTERS:String = "9_1_1";
      
      public static const REQUEST_FINISH:String = "9_1_2";
      
      public static const GET_RANK:String = "9_1_3";
      
      public static const GET_TODAY_TIMES:String = "9_1_4";
      
      public static const GET_USER_DETAIL:String = "9_1_5";
      
      public static const GET_LEVEL_BONUS:String = "9_1_6";
      
      public static const CMD_REFRESH_ITEM:String = "9_1_7";
      
      public static const REQUEST_JOIN_PVE_WITH_BUFF:String = "9_1_8";
      
      public static const CMD_ADD_TODAY_TIMES:String = "9_1_9";
      
      public static const CMD_GET_ADDED_TIME:String = "9_1_11";
      
      public static const CMD_CLEAR_ITEM:String = "9_1_18";
      
      public static const ALL_MONSTER_CLEAR:String = "9_1_12";
      
      public static const CMD_LONGBI_POWER:String = "9_1_14";
      
      public static const PLUG_GET_INFO:String = "9_6_1";
      
      public static const PLUG_START:String = "9_6_2";
      
      public static const PLUG_PAUSE:String = "9_6_3";
      
      public static const PLUG_RES:String = "9_6_4";
      
      public static const PLUG_LONGBI_POWER:String = "9_6_5";
      
      public static const CMD_GET_BTINFO_BATCH:String = "9_1_35";
      
      public static const CMD_BUY_BUFF_INSIND_BT:String = "9_1_37";
      
      public static const NOTIFY_BTRANK_FINISH_BONUS:String = "9_1_39";
      
      public static const CMD_GET_MOD:String = "9_1_40";
      
      public static const CMD_REFRESH_MOD:String = "9_1_41";
      
      public static const CMD_TIME_MOD_IS_SUCCESS:String = "9_1_42";
      
      public static const CMD_GET_DAILYBT_PASSTIME_BATCH:String = "9_1_19";
      
      public static const CMD_GET_CD:String = "9_1_45";
      
      public static const CMD_CLEAR_CD:String = "9_1_46";
      
      public static const CMD_GET_CD_BATCH:String = "9_1_47";
      
      public static const CMD_GET_DAMAGE_RANGE:String = "9_1_50";
      
      public static const CMD_KILL_CREEP_MANUAL:String = "9_1_51";
      
      public static const CMD_CAN_JOIN:String = "9_1_53";
      
      public static const CMD_REMOVE_CREEP_MANUAL:String = "9_1_55";
      
      public static const CMD_REVIVE:String = "9_1_15";
      
      public static const CMD_GET_CAN_JOIN_BATCH:String = "9_1_56";
      
      public function PVECommonClient()
      {
         super();
      }
      
      public static function get instance() : PVECommonClient
      {
         if(_instance == null)
         {
            _instance = new PVECommonClient();
         }
         return _instance;
      }
      
      public function sendRequestJoinPVEScene(param1:uint, param2:uint, param3:Boolean = false) : void
      {
         if(param3)
         {
            this.sendXtMessage(REQUEST_JOIN_PVE,{
               "id":param1,
               "li":param2,
               "sg":param3
            });
         }
         else
         {
            this.sendXtMessage(REQUEST_JOIN_PVE,{
               "id":param1,
               "li":param2
            });
         }
      }
      
      public function sendRefreshMonsters(param1:uint) : void
      {
         this.sendXtMessage(REFRESH_MONSTERS,{"ri":param1});
      }
      
      public function sendRefreshItem(param1:uint) : void
      {
         this.sendXtMessage(CMD_REFRESH_ITEM,{"ri":param1});
      }
      
      public function sendBuyTimes(param1:uint, param2:uint) : void
      {
         this.sendXtMessage(CMD_ADD_TODAY_TIMES,{
            "id":param1,
            "num":param2
         });
      }
      
      public function sendGetBuyTimes(param1:uint) : void
      {
         this.sendXtMessage(CMD_GET_ADDED_TIME,{"id":param1});
      }
      
      public function sendClearItem() : void
      {
         this.sendXtMessage(CMD_CLEAR_ITEM,null);
      }
      
      public function getRank(param1:uint) : void
      {
         this.sendXtMessage(GET_RANK,{
            "id":1,
            "ty":param1
         });
      }
      
      public function buyBuffInside(param1:uint) : void
      {
         this.sendXtMessage(CMD_BUY_BUFF_INSIND_BT,{"id":param1});
      }
      
      public function getTodayTimes(param1:int) : void
      {
         this.sendXtMessage(GET_TODAY_TIMES,{"id":param1});
      }
      
      public function getUserDetail(param1:int, param2:String) : void
      {
         this.sendXtMessage(GET_USER_DETAIL,{
            "id":param1,
            "nn":param2
         });
      }
      
      public function sendFinishPVE(param1:Boolean = false, param2:int = 0) : void
      {
         this.sendXtMessage(REQUEST_FINISH,{
            "fo":param1,
            "seq":param2
         });
      }
      
      public function getLevelBonus() : void
      {
         this.sendXtMessage(GET_LEVEL_BONUS,null);
      }
      
      public function useLongbiPower(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_LONGBI_POWER,{
            "id":param1,
            "li":param2
         });
      }
      
      public function getPlugInfo(param1:int) : void
      {
         this.sendXtMessage(PLUG_GET_INFO,{"id":param1});
      }
      
      public function startPlug(param1:int) : void
      {
         this.sendXtMessage(PLUG_START,{"id":param1});
      }
      
      public function pausePlug(param1:int) : void
      {
         this.sendXtMessage(PLUG_PAUSE,{"id":param1});
      }
      
      public function plugLongbiPower(param1:int) : void
      {
         this.sendXtMessage(PLUG_LONGBI_POWER,{"id":param1});
      }
      
      public function joinPveWithBuffer(param1:uint, param2:uint, param3:uint) : void
      {
         this.sendXtMessage(REQUEST_JOIN_PVE_WITH_BUFF,{
            "id":param1,
            "li":param2,
            "buff":param3
         });
      }
      
      public function getUserInfoBatch(param1:Array) : void
      {
         this.sendXtMessage(CMD_GET_BTINFO_BATCH,{"id":param1});
      }
      
      public function getChallengeType(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_MOD,{"id":param1});
      }
      
      public function selectChallengeType(param1:int) : void
      {
         this.sendXtMessage(CMD_REFRESH_MOD,{"id":param1});
      }
      
      public function getChallengeState() : void
      {
         this.sendXtMessage(CMD_TIME_MOD_IS_SUCCESS,null);
      }
      
      public function getDailyUserInfoBatch(param1:Array) : void
      {
         this.sendXtMessage(CMD_GET_DAILYBT_PASSTIME_BATCH,{"id":param1});
      }
      
      public function getCd(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_CD,{"id":param1});
      }
      
      public function clearCd(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_CLEAR_CD,{
            "id":param1,
            "ly":param2
         });
      }
      
      public function getBatchCdInfo(param1:Array) : void
      {
         this.sendXtMessage(CMD_GET_CD_BATCH,{"id":param1});
      }
      
      public function getDamageRange(param1:Boolean, param2:Array = null) : void
      {
         this.sendXtMessage(CMD_GET_DAMAGE_RANGE,{
            "a":param1,
            "t":param2
         });
      }
      
      public function canJoin(param1:int) : void
      {
         this.sendXtMessage(CMD_CAN_JOIN,{"id":param1});
      }
      
      public function removeCreepManual(param1:int) : void
      {
         this.sendXtMessage(CMD_REMOVE_CREEP_MANUAL,{"ci":param1});
      }
      
      public function revivePlayer(param1:Boolean = false) : *
      {
         this.sendXtMessage(CMD_REVIVE,{"lb":param1});
      }
      
      public function getCanJoinBatch(param1:String) : *
      {
         this.sendXtMessage(CMD_GET_CAN_JOIN_BATCH,{"ids":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

