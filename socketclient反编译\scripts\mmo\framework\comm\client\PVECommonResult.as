package mmo.framework.comm.client
{
   public class PVECommonResult
   {
      public static const SUCCESS:int = 0;
      
      private static var errorMap:Object = {
         "1":"不能进入。",
         "2":"创建房间/战场出错。",
         "3":"刷怪出错。",
         "4":"不能结束：没死并且没完成PVE所有必须完成的层。",
         "5":"今日的挑战次数已经用完，请明日再来吧！",
         "6":"加物品出错。",
         "7":"不能通关。",
         "8":"不满足buff的条件。",
         "9":"不能增加次数了。",
         "10":"龙币不足。",
         "11":"超过每日消费上限。",
         "12":"超过每月消费上限。",
         "13":"金币不足。",
         "100":"参数错误。"
      };
      
      public function PVECommonResult()
      {
         super();
      }
      
      public static function getErrorMes(param1:String) : String
      {
         if(errorMap[param1] as String != null)
         {
            return errorMap[param1] as String;
         }
         return "系统出错：" + param1;
      }
   }
}

