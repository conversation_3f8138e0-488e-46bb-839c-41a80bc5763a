package mmo.framework.comm.corps
{
   import mmo.common.utils.event.OnceEventListener;
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class CorpsClient
   {
      public static const CMD_CHECK_ARMY_NAME:String = "65_0";
      
      public static const CMD_CREATE_ARMY:String = "65_1";
      
      public static const CMD_GET_MY_ARMY_INFO:String = "65_2";
      
      public static const CMD_GET_MY_ARMY_All_MEMBERS:String = "65_3";
      
      public static const CMD_GET_OUT:* = "65_4";
      
      public static const CMD_CHANGE_DUTY:String = "65_5";
      
      public static const CMD_CHANGE_INFORM:String = "65_6";
      
      public static const CMD_CHANGE_DESCRIPTION:String = "65_7";
      
      public static const CMD_SEND_REQUEST_JOIN:String = "65_8";
      
      public static const CMD_HANDLE_REQUEST_JOIN:String = "65_9";
      
      public static const CMD_SEND_INVITE_JOIN:String = "65_10";
      
      public static const CMD_HANDLE_INVITE_JOIN:String = "65_11";
      
      public static const CMD_GET_RANDOM_CHARS:String = "65_12";
      
      public static const CMD_GET_ALL_ARMY_WITH_LIKE_NAME:String = "65_13";
      
      public static const CMD_WATER_TREE_OF_CORPS:String = "65_14";
      
      public static const CMD_GET_RECOMMEND_CORPS_LIST:String = "65_15";
      
      public static const CMD_GET_AND_JOIN_MY_TERRITORY:String = "65_16";
      
      public static const CMD_GET_ARMY_EVENT:String = "65_17";
      
      public static const CMD_VIEW_OTHER_ARMY_INFO:String = "65_18";
      
      public static const CMD_VIEW_ARMY_INFO_BY_ARMYNAME:String = "65_19";
      
      public static const CMD_GET_ARMY_REMAIN_RECMDTIMES:String = "65_21";
      
      public static const CMD_RECOMMEND_ARMY:String = "65_22";
      
      public static const CMD_ARMY_BONUS_BOX_INFO:String = "65_23";
      
      public static const CMD_OPEN_BONUS_BOX:String = "65_24";
      
      public static const CMD_ARMY_RANK:String = "65_25";
      
      public static const CMD_ARMY_REC_TOTALPAGE:String = "65_26";
      
      public static const CMD_GET_LEVEL_UP_BONUS_STATE:String = "65_30";
      
      public static const CMD_GAIN_LEVEL_UP_BONUS:String = "65_31";
      
      public static const CMD_DEFAULT_JOIN_SETTING:String = "65_32";
      
      public static const CMD_MODIFY_JOIN_SETTING:String = "65_33";
      
      public static const CMD_SET_ACCEPT_ARMY_INVITE:String = "65_34";
      
      public static const CMD_SET_ARMY_SKIN:String = "65_35";
      
      public static const CMD_BUY_ARMY_SKIN:String = "65_36";
      
      public static const CMD_UPGRADE_BOX:String = "65_40";
      
      public static const CMD_GET_ALL_GUARDIAN:String = "65_41";
      
      public static const CMD_BUND_GUARDIAN:* = "65_42";
      
      public static const CMD_GET_GUARDIAN_INFO:String = "65_43";
      
      public static const CMD_PLAY_WITH:String = "65_44";
      
      public static const CMD_FEED_GUARDIAN:String = "65_45";
      
      public static const CMD_GET_MATURE_BONUS:String = "65_46";
      
      public static const CMD_DUTY_LIMIT:String = "65_50";
      
      public static const CMD_LEVELUP_DUTY:String = "65_51";
      
      public static const CMD_VS_GUARDIAN_RECOMMEND:String = "65_53";
      
      public static const CMD_ARMY_GUARDIANS:String = "65_54";
      
      public static const CMD_TRYTO_FIGHT_GUARDIAN:String = "65_55";
      
      public static const CMD_REFRESH_GUARDIAN_CREEP:String = "65_56";
      
      public static const CMD_FIGHT_FINISH_BONUS:String = "65_57";
      
      public static const CMD_OTHER_ARMY_WATER_INFO:String = "65_60";
      
      public static const CMD_WATER_OTHER_ARMY:String = "65_61";
      
      public static const CMD_UPGRADE_TREE:String = "65_62";
      
      public static const CMD_ARMY_ARC_LEVEL:String = "65_63";
      
      public static const CMD_LOAD_ARMY_SKILL_MEMBER:String = "65_64";
      
      public static const CMD_VIEW_ARMY_SKILL_LIST_RESEARCH:String = "65_65";
      
      public static const CMD_RESEARCH_ARMY_SKILL:String = "65_66";
      
      public static const CMD_VIEW_ARMY_SKILL_LIST_LEARN:String = "65_67";
      
      public static const CMD_LEARN_ARMY_SKILL:String = "65_68";
      
      public static const CMD_GET_CD:String = "65_80";
      
      public static const CMD_GET_QUICK_JOIN:String = "65_81";
      
      public static const CMD_VIEW_MEMBER_WEEKLY_CONTRIBUTE_RANK:String = "65_82";
      
      public static const CMD_GET_WEEKLY_BONUS_STATE:String = "65_88";
      
      public static const CMD_GAIN_WEEKLY_BONUS:String = "65_89";
      
      public static const CMD_BOSS_PANEL_VIEW_INFO:String = "65_4_1";
      
      public static const CMD_GET_ARMY_BOSS_CUP:String = "65_4_2";
      
      public static const CMD_GET_MY_BOSS_LV_RANK_INFO:String = "65_4_3";
      
      public static const CMD_GET_ARMY_BOSS_LV_RANK_INFO:String = "65_4_4";
      
      public static const CMD_GET_ELITE_CHALLENGE_INFO:String = "65_4_5";
      
      public static const CMD_START_ELITE_CHALLENGE:String = "65_4_6";
      
      public static const CMD_NOTIFY_ELITE_CHALLENGE:String = "65_4_7";
      
      public static const CMD_READY_IN_GAME_ROOM:String = "65_4_8";
      
      public static const CMD_FINISH_ELITE_CHALLENGE:String = "65_4_9";
      
      public static const CMD_FINISH_NORMAL_CHALLENGE:String = "64_4_10";
      
      public static const CMD_REFRESH_ELITE_DAMAGE:String = "64_4_11";
      
      public static const CMD_RESURRECTION:String = "64_4_12";
      
      public static const CMD_TRAIN_CROPS_GET_BONUS:String = "65_83";
      
      public static const CMD_TRAIN_CROPS_BONUS_INFO:String = "65_84";
      
      public static const CMD_GET_RANDOM_ARMY:String = "65_85";
      
      private static var _instance:CorpsClient = null;
      
      private static const EXTENSION_NAME:String = ExtMap.ArmyExtention;
      
      public function CorpsClient()
      {
         super();
      }
      
      public static function get instance() : CorpsClient
      {
         if(_instance == null)
         {
            _instance = new CorpsClient();
         }
         return _instance;
      }
      
      public function checkCorpsNameIsLegal(param1:String) : void
      {
         this.sendXtMessage(CMD_CHECK_ARMY_NAME,{"an":param1});
      }
      
      public function createCorps(param1:String, param2:String) : void
      {
         this.sendXtMessage(CMD_CREATE_ARMY,{
            "fn":param1,
            "info":param2
         });
      }
      
      public function loadMyCorpInfo() : void
      {
         this.sendXtMessage(CMD_GET_MY_ARMY_INFO,{});
      }
      
      public function loadMyCorpMemberInfo() : void
      {
         this.sendXtMessage(CMD_GET_MY_ARMY_All_MEMBERS,{});
      }
      
      public function getOutMenber(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_OUT,{"ci":param1});
      }
      
      public function changeDuty(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_CHANGE_DUTY,{
            "dt":param1,
            "ci":param2
         });
      }
      
      public function changeNotice(param1:String) : void
      {
         this.sendXtMessage(CMD_CHANGE_INFORM,{"info":param1});
      }
      
      public function changeIntro(param1:String) : void
      {
         this.sendXtMessage(CMD_CHANGE_DESCRIPTION,{"des":param1});
      }
      
      public function applyJoin(param1:String, param2:int) : void
      {
         this.sendXtMessage(CMD_SEND_REQUEST_JOIN,{
            "an":param1,
            "ai":param2
         });
      }
      
      public function handleRequestJoin(param1:String, param2:String, param3:Boolean) : void
      {
         this.sendXtMessage(CMD_HANDLE_REQUEST_JOIN,{
            "nm":param1,
            "fn":param2,
            "ag":param3
         });
      }
      
      public function sendInviteJoin(param1:String) : void
      {
         this.sendXtMessage(CMD_SEND_INVITE_JOIN,{"nm":param1});
      }
      
      public function handleInviteJoin(param1:String, param2:String, param3:Boolean) : void
      {
         this.sendXtMessage(CMD_HANDLE_INVITE_JOIN,{
            "nm":param1,
            "fn":param2,
            "ag":param3
         });
      }
      
      public function searchRandomPlayers() : void
      {
         this.sendXtMessage(CMD_GET_RANDOM_CHARS,{});
      }
      
      public function searchCorpsByName(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_ALL_ARMY_WITH_LIKE_NAME,{"ln":param1});
      }
      
      public function getRecommendCorpsList(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_RECOMMEND_CORPS_LIST,{"pn":param1});
      }
      
      public function getMyCorpsTerritoryRoomName(param1:int = -1, param2:int = 0) : void
      {
         this.sendXtMessage(CMD_GET_AND_JOIN_MY_TERRITORY,{
            "ai":param1,
            "index":param2
         });
      }
      
      public function waterTreeOfCorps() : void
      {
         this.sendXtMessage(CMD_WATER_TREE_OF_CORPS,null);
      }
      
      public function getCorpsEvent(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_ARMY_EVENT,{"typ":param1});
      }
      
      public function getOtherCorpsInfoByCid(param1:int) : void
      {
         this.sendXtMessage(CMD_VIEW_OTHER_ARMY_INFO,{"ci":param1});
      }
      
      public function getOtherCorpsInfoByCorpsName(param1:String) : void
      {
         this.sendXtMessage(CMD_VIEW_ARMY_INFO_BY_ARMYNAME,{"fm":param1});
      }
      
      public function getCorpsRemainRecommendTimes() : void
      {
         this.sendXtMessage(CMD_GET_ARMY_REMAIN_RECMDTIMES,{});
      }
      
      public function recommendCorps(param1:int) : void
      {
         this.sendXtMessage(CMD_RECOMMEND_ARMY,{"type":param1});
      }
      
      public function getCorpsBonusBoxInfo() : void
      {
         this.sendXtMessage(CMD_ARMY_BONUS_BOX_INFO,{});
      }
      
      public function openBonusBox() : void
      {
         this.sendXtMessage(CMD_OPEN_BONUS_BOX,{});
      }
      
      public function getCorpsRank(param1:int) : void
      {
         this.sendXtMessage(CMD_ARMY_RANK,{"type":param1});
      }
      
      public function getCorpsRecommendTotalPage() : void
      {
         this.sendXtMessage(CMD_ARMY_REC_TOTALPAGE,{});
      }
      
      public function getLevelUpBonusState() : void
      {
         this.sendXtMessage(CMD_GET_LEVEL_UP_BONUS_STATE,null);
      }
      
      public function gainLevelUpBonus(param1:int) : void
      {
         this.sendXtMessage(CMD_GAIN_LEVEL_UP_BONUS,{"lv":param1});
      }
      
      public function upgradeTreasury() : void
      {
         this.sendXtMessage(CMD_UPGRADE_BOX,null);
      }
      
      public function getCorpsTreeInfo(param1:String) : void
      {
         this.sendXtMessage(CMD_OTHER_ARMY_WATER_INFO,{"an":param1});
      }
      
      public function waterCorpsTree(param1:String, param2:Boolean = false) : void
      {
         this.sendXtMessage(CMD_WATER_OTHER_ARMY,{
            "an":param1,
            "lb":param2
         });
      }
      
      public function upgradeCorpsTree() : void
      {
         this.sendXtMessage(CMD_UPGRADE_TREE,null);
      }
      
      public function getCorpsBuildsLv(param1:String) : void
      {
         this.sendXtMessage(CMD_ARMY_ARC_LEVEL,{"an":param1});
      }
      
      public function getJoinSetting() : void
      {
         this.sendXtMessage(CMD_DEFAULT_JOIN_SETTING,null);
      }
      
      public function setJoinSetting(param1:int, param2:int = -1) : void
      {
         this.sendXtMessage(CMD_MODIFY_JOIN_SETTING,{
            "st":param1,
            "ct":param2
         });
      }
      
      public function setCorpInviteSetting(param1:int) : void
      {
         this.sendXtMessage(CMD_SET_ACCEPT_ARMY_INVITE,{"st":param1});
      }
      
      public function setCorpsAttire(param1:int) : void
      {
         this.sendXtMessage(CMD_SET_ARMY_SKIN,{"sid":param1});
      }
      
      public function buyCorpsAttire(param1:int) : void
      {
         this.sendXtMessage(CMD_BUY_ARMY_SKIN,{"sid":param1});
      }
      
      public function getAllGuardInfo() : void
      {
         this.sendXtMessage(CMD_GET_ALL_GUARDIAN,null);
      }
      
      public function makeGuardContract(param1:int) : void
      {
         this.sendXtMessage(CMD_BUND_GUARDIAN,{"gti":param1});
      }
      
      public function getGuardInfo(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_GUARDIAN_INFO,{"gi":param1});
      }
      
      public function playWithGuard(param1:int) : void
      {
         this.sendXtMessage(CMD_PLAY_WITH,{"gi":param1});
      }
      
      public function feedGuard(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_FEED_GUARDIAN,{
            "gi":param1,
            "ft":param2
         });
      }
      
      public function getGuardBonus(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_MATURE_BONUS,{"gi":param1});
      }
      
      public function getPostInfo() : void
      {
         this.sendXtMessage(CMD_DUTY_LIMIT,null);
      }
      
      public function postLvUp(param1:int) : void
      {
         this.sendXtMessage(CMD_LEVELUP_DUTY,{"duty":param1});
      }
      
      public function getGuardFightCorpsList() : void
      {
         this.sendXtMessage(CMD_VS_GUARDIAN_RECOMMEND,null);
      }
      
      public function getCorpsGuardList(param1:String) : void
      {
         this.sendXtMessage(CMD_ARMY_GUARDIANS,{"an":param1});
      }
      
      public function tryFightGuard(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(CMD_TRYTO_FIGHT_GUARDIAN,{
            "gi":param1,
            "tai":param2,
            "tgi":param3
         });
      }
      
      public function refreshGuardCreep(param1:int) : void
      {
         this.sendXtMessage(CMD_REFRESH_GUARDIAN_CREEP,{"rei":param1});
      }
      
      public function loadCorpsSkill(param1:int) : void
      {
         this.sendXtMessage(CMD_LOAD_ARMY_SKILL_MEMBER,null);
      }
      
      public function getCorpsSkillResearchList() : void
      {
         this.sendXtMessage(CMD_VIEW_ARMY_SKILL_LIST_RESEARCH,null);
      }
      
      public function researchCorpsSkill(param1:int) : void
      {
         this.sendXtMessage(CMD_RESEARCH_ARMY_SKILL,{"s":param1});
      }
      
      public function getCorpsSkillLearnList() : void
      {
         this.sendXtMessage(CMD_VIEW_ARMY_SKILL_LIST_LEARN,null);
      }
      
      public function learnCorpsSkill(param1:int) : void
      {
         this.sendXtMessage(CMD_LEARN_ARMY_SKILL,{"s":param1});
      }
      
      public function getQuickJoinCD() : void
      {
         this.sendXtMessage(CMD_GET_CD,null);
      }
      
      public function quickJoin() : void
      {
         this.sendXtMessage(CMD_GET_QUICK_JOIN,null);
      }
      
      public function viewContributeRank(param1:int) : void
      {
         this.sendXtMessage(CMD_VIEW_MEMBER_WEEKLY_CONTRIBUTE_RANK,{"id":param1});
      }
      
      public function getBossPanelInfo() : void
      {
         this.sendXtMessage(CMD_BOSS_PANEL_VIEW_INFO);
      }
      
      public function getBossCupInfo(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_ARMY_BOSS_CUP,{"an":param1});
      }
      
      public function getMyBossRankInfo() : void
      {
         this.sendXtMessage(CMD_GET_MY_BOSS_LV_RANK_INFO);
      }
      
      public function getBossRankInfo(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_ARMY_BOSS_LV_RANK_INFO,{"p":param1});
      }
      
      public function getEliteChallengeInfo() : void
      {
         this.sendXtMessage(CMD_GET_ELITE_CHALLENGE_INFO);
      }
      
      public function startEliteChallenge(param1:Boolean) : void
      {
         this.sendXtMessage(CMD_START_ELITE_CHALLENGE,{"sn":param1});
      }
      
      public function readyEliteChallenge() : void
      {
         this.sendXtMessage(CMD_READY_IN_GAME_ROOM);
      }
      
      public function resurrectionInEliteBoss(param1:Boolean) : void
      {
         this.sendXtMessage(CMD_RESURRECTION,{"lb":param1});
      }
      
      public function getRandomArmy() : void
      {
         this.sendXtMessage(CMD_GET_RANDOM_ARMY);
      }
      
      public function getWeekContributeState(param1:Function) : void
      {
         this.sendXtMsgWithCallBack(CMD_GET_WEEKLY_BONUS_STATE,param1);
      }
      
      public function getWeekContributeBonus(param1:int, param2:Function) : void
      {
         this.sendXtMsgWithCallBack(CMD_GAIN_WEEKLY_BONUS,param2,{"index":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object = null) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
      
      private function sendXtMsgWithCallBack(param1:String, param2:Function, param3:Object = null) : void
      {
         OnceEventListener.add(SocketClient.instance,param1,param2);
         this.sendXtMessage(param1,param3);
      }
   }
}

