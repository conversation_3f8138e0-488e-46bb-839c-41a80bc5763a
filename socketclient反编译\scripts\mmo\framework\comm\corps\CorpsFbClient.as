package mmo.framework.comm.corps
{
   import flash.events.Event;
   import flash.events.IEventDispatcher;
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class CorpsFbClient
   {
      private static var _instance:CorpsFbClient;
      
      private static const EXTENSION_NAME:String = ExtMap.CorpsFbExtension;
      
      public static const CMD_LOAD_FB_RATE:* = "65_6_1";
      
      public static const CMD_QUERY_JOIN_RECORD:* = "65_6_2";
      
      public static const CMD_QUERY_CHAR_DAMAGE:* = "65_6_3";
      
      public static const CMD_QUERY_DAMAGE_RANK:* = "65_6_4";
      
      public static const CMD_QUERY_ACTIVITY_RANK:* = "65_6_5";
      
      public static const CMD_RESET_FB:* = "65_6_6";
      
      public static const CMD_RESET_FB_USE_LONGBI:* = "65_6_7";
      
      public static const CMD_START_CHALLENGE:* = "65_6_10";
      
      public static const CMD_REFRESH_CREEP:* = "65_6_11";
      
      public static const NOTIFY_FINISH:* = "65_6_12";
      
      public static const CMD_LOAD_STORE_INFO:* = "65_6_100";
      
      public static const CMD_BUY_GOODS:* = "65_6_101";
      
      public static const CMD_MANUAL_REFRESH_GOODS:* = "65_6_102";
      
      public static const CMD_LOAD_ALL_GOODS_DEFINE:* = "65_6_103";
      
      public static const NOTIFY_STORE_GOODS_UPDATE:* = "65_6_104";
      
      public static const CMD_JOIN_APPLY_QUEUE:* = "65_6_120";
      
      public static const CMD_CHANGE_APPLY_QUEUE:* = "65_6_121";
      
      public static const CMD_LOAD_ALL_APPLY_INFO:* = "65_6_122";
      
      public static const CMD_LOAD_ONE_APPLY_DETAIL:* = "65_6_123";
      
      public static const CMD_LEADER_ASSIGN_BOOTY:* = "65_6_124";
      
      public static const CMD_LEADER_COMBINE_BONUS:* = "65_6_125";
      
      internal var result:CorpsFbResult;
      
      public function CorpsFbClient()
      {
         super();
      }
      
      public static function get instance() : CorpsFbClient
      {
         if(_instance == null)
         {
            _instance = new CorpsFbClient();
         }
         return _instance;
      }
      
      public function loadFbInfo(param1:Function = null) : void
      {
         this.sendXtMessage(CMD_LOAD_FB_RATE,{},param1);
      }
      
      public function queryJoinRecord(param1:Function = null) : void
      {
         this.sendXtMessage(CMD_QUERY_JOIN_RECORD,{},param1);
      }
      
      public function queryFbDamage(param1:int, param2:Function = null) : void
      {
         this.sendXtMessage(CMD_QUERY_CHAR_DAMAGE,{"fid":param1},param2);
      }
      
      public function queryFbDamageRate(param1:int, param2:Function = null) : void
      {
         this.sendXtMessage(CMD_QUERY_DAMAGE_RANK,{"fid":param1},param2);
      }
      
      public function qureyFbActivityRank(param1:Function = null) : void
      {
         this.sendXtMessage(CMD_QUERY_ACTIVITY_RANK,{},param1);
      }
      
      public function starChallenge(param1:int, param2:int, param3:Function = null) : void
      {
         this.sendXtMessage(CMD_START_CHALLENGE,{
            "fid":param1,
            "gid":param2
         },param3);
      }
      
      public function refreshCreep(param1:Function = null) : void
      {
         this.sendXtMessage(CMD_REFRESH_CREEP,{},param1);
      }
      
      public function resetFb(param1:int, param2:Function = null) : void
      {
         this.sendXtMessage(CMD_RESET_FB,{"fid":param1},param2);
      }
      
      public function resetFbUseLongBi(param1:int, param2:Function = null) : void
      {
         this.sendXtMessage(CMD_RESET_FB_USE_LONGBI,{"fid":param1},param2);
      }
      
      public function add(param1:IEventDispatcher, param2:String, param3:Function, param4:Boolean = true, param5:int = 0) : void
      {
         var getResult:Function = null;
         var currentTarget:IEventDispatcher = param1;
         var type:String = param2;
         var callBack:Function = param3;
         var lockScreen:Boolean = param4;
         var priority:int = param5;
         getResult = function(param1:Event):void
         {
            currentTarget.removeEventListener(type,getResult);
            if(callBack != null)
            {
               callBack.call(null,param1);
            }
         };
         currentTarget.addEventListener(type,getResult,false,priority);
      }
      
      private function sendXtMessage(param1:String, param2:Object = null, param3:Function = null) : void
      {
         this.add(SocketClient.instance,param1,param3);
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

