package mmo.framework.comm.corps
{
   public class CorpsFbResult
   {
      public static const MAX_TIME:String = "max times";
      
      public static const HAS_PASS_GATE:String = "has pass gate";
      
      public static const FB_NOT_OPEN:String = "not open";
      
      public static const HAS_CHALLENGER:String = "has challenger";
      
      public static const ID2TEXT:Object = {
         "max times":"超过每天最大进入次数！",
         "has pass gate":"该关卡已经通关！",
         "not open":"该副本还没开启！",
         "has challenger":"已经有人在里面挑战了！",
         "not pass preGate":"上一个关卡还未通关"
      };
      
      public function CorpsFbResult()
      {
         super();
      }
      
      public static function isSuccess(param1:Object) : Boolean
      {
         return param1.msg == null;
      }
   }
}

