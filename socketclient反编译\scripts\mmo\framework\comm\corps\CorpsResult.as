package mmo.framework.comm.corps
{
   public class CorpsResult
   {
      public static const SUCCESS:int = 1;
      
      public static const FAILURE:int = 2;
      
      public static const HAVE_NOT_JION_FAMILY:int = 3;
      
      public static const NO_SATIFY:int = 4;
      
      public static const THE_DUTY_IS_OUT:int = 5;
      
      public static const FAMILY_NAME_IS_ERORR:int = 6;
      
      public static const DB_ERROR:int = 7;
      
      public static const FAMILY_INFO_ERORR:int = 8;
      
      public static const NO_PURVIEW:int = 9;
      
      public static const CAN_NOT_EXIT:int = 10;
      
      public static const PARAMS_ERROR:int = 11;
      
      public static const NO_VIP:int = 12;
      
      public static const NO_ENOUGH_GOLD:int = 13;
      
      public static const KICK_OUT_TIMES_LIMIT:int = 14;
      
      public static const NO_THIS_CHARACTER:int = 15;
      
      public static const INVITE_INFO_OUT_TIME:int = 16;
      
      public static const THIS_FAMILY_IS_UN_ACTIVE:int = 17;
      
      public static const HAS_JOIN_FAMILY:int = 18;
      
      public static const TOTAL_NUM_LIMIT:int = 19;
      
      public static const CREATE_TIME_LIMIT:int = 20;
      
      public static const TIMES_LIMIT:int = 21;
      
      public static const FAMILY_TIMES_LIMIT:int = 22;
      
      public static const TAR_HAS_JOIN_FAMILY:int = 23;
      
      public static const TODAY_HAVE_OUT:int = 24;
      
      public static const MATERAIL_CONDITION_LACK:int = 25;
      
      public static const exit_times_limit:int = 26;
      
      public static const HE_TODAY_HAVE_OUT:int = 27;
      
      public static const FAMILY_TASK_TIMES_LIMIT:int = 28;
      
      public static const FAMILY_LEVEL_NO_ENGOUH:int = 29;
      
      public static const FAMILY_BOSS_TIMES_LIMIT:int = 30;
      
      public static const NO_HAVE_THE_Bonus:int = 31;
      
      public static const BAG_FULL:int = 32;
      
      public static const HAVE_GET_IT:int = 33;
      
      public static const RMI_CONNETION_ERROR:int = 34;
      
      public static const MAX_REC_TIMES:int = 35;
      
      public static const ARMY_VS_CREEP_EXCEED_USER_COUNT_LIMIT:int = 36;
      
      public static const ARMY_LEVEL_NOT_ENOUGH:int = 37;
      
      public static const CHAR_CONTRIBUTE_NOT_ENOUGH:int = 38;
      
      public static const ANTIADDITION_STATUS:int = 39;
      
      public static const ARMY_NOT_EXIST:int = 40;
      
      public static const ARMY_TODAY_COUNT_LIMIT:int = 41;
      
      public static const CHAR_WATER_OTHER_COUNT_LIMIT:int = 42;
      
      public static const HAS_WATER_TARGET:int = 43;
      
      public static const ARMY_BOX_EXP_NOT_ENOUGH:int = 44;
      
      public static const ARMY_BOX_FUND_NOT_ENOUGH:int = 45;
      
      public static const ARMY_REFUSE_JOIN:int = 46;
      
      public static const ARMY_AUTO_JOIN:int = 47;
      
      public static const NOT_AUTO_JOIN_CONDITION:int = 48;
      
      public static const NOT_ACCEPT_JOIN_INVITE:int = 49;
      
      public static const ARMY_GUARDIAN_NOT_EXOST:int = 50;
      
      public static const ARMY_GUARDIAN_HAS_EXOST:int = 51;
      
      public static const ARMY_GUARDIAN_NOT_MATURE:int = 52;
      
      public static const ARMY_DAILY_LIMIT:int = 53;
      
      public static const ARMY_HAS_GAIN_BONUS:int = 54;
      
      public static const ARMY_FRIENDLESS_NOT_ENOUGH:int = 55;
      
      public static const DUTY_LEVEL_FULL:int = 56;
      
      public static const ARMY_SKILL_RESEARCH_EXCEED_MAX_LEVEL:int = 57;
      
      public static const GUARDIAN_BE_FIGTH_TIMES_LIMIT:int = 58;
      
      public static const NOT_ENOUGH_LONGBI:int = 59;
      
      public static const ARMY_SKILL_RESEARCH_COOL_DOWN_IS_NOT_FINISHED:int = 60;
      
      public static const ARMY_SKILL_CAN_NOT_LEARN_DUTY_OR_NOT_ENOUGH_CONTRIBUTION:int = 61;
      
      public static const NO_FIXED_ARMY:int = 62;
      
      public static const NOT_COOL_DOWN:int = 63;
      
      public static const HAS_INVITE:int = 64;
      
      public static const ARMY_NOT_FULL:int = 65;
      
      public static const ARMY_ACTIVITY_OUTDAY:int = 66;
      
      public static const ARMY_NAME_HAS_BEEN_REQUEST:int = 71;
      
      public static const ID2TEXT:Object = {
         "1":"成功",
         "2":"未知错误",
         "3":"还没加入军团",
         "4":"创建条件不足",
         "5":"该职位人数已经达到上限啦。",
         "6":"你的军团名称已经有人使用了，再换一个军团名称吧！",
         "7":"数据库操作失败",
         "8":"军团简介或公告错误",
         "9":"权限不够",
         "10":"不能退出",
         "11":"前端参数错误",
         "12":"非vip",
         "13":"金币不足",
         "14":"每天开除的族员不能超过15个哦！",
         "15":"没有这个角色",
         "16":"邀请信息过时",
         "17":"这个军团已经解散",
         "18":"已经加入军团",
         "19":"军团人数已满",
         "20":"每天创建次数满",
         "21":"每日次数满~",
         "22":"你今天已经浇过水了，明天再来浇吧~",
         "23":"对方已经加入军团",
         "24":"刚刚离开了军团，明天再加入吧！",
         "25":"材料不够",
         "26":"每天离开军团的人数不能超过5个人，明天再离开吧",
         "27":"他刚刚离开过军团，明天邀请吧",
         "28":"军团任务次数满",
         "29":"军团等级不够",
         "30":"本周挑战机会已经用完了",
         "31":"没有获得奖励的资格",
         "32":"你的背包已经满了，请清理一下再过来吧！",
         "33":"已经领取",
         "34":"rmi连接错误",
         "35":"已达到最大军团推荐次数！",
         "36":"超出军团挑战人数限制",
         "37":"军团等级不够!",
         "38":"个人贡献不够!",
         "39":"你已经冒险很长时间，不能获得奖励了，请明天再来吧！",
         "40":"军团不存在!",
         "41":"军团被非成员浇水的每日次数满!",
         "42":"你今天已经不能再为其他军团之树浇水了，明天再来吧。",
         "43":"同一个军团每天只能浇水1次哦，还是去其他军团吧。",
         "44":"军团升级经验不足！",
         "45":"军团资金不足！",
         "46":"该军团暂不允许新成员加入。",
         "47":"军团设置 自动加入",
         "48":"未达到目标军团自动加入的条件",
         "49":"玩家不接受军团邀请",
         "50":"军团守护兽不存在！",
         "51":"该类型军团守护兽不存在！",
         "52":"已有尚未达到40级的军团守护神兽，不能再契约新的守护神兽。",
         "53":"今天已经造成最多伤害了，明天再来吧！",
         "54":"已经领取过奖励",
         "55":"你跟该守护神兽的亲密值不足，不能领取哦。",
         "56":"已达到升级上限",
         "57":"达到军团技能等级上限",
         "58":"神兽每天被征战达到上限",
         "59":"龙币不足!",
         "60":"军团技能研究仍旧在冷却时间中",
         "61":"你还达不到该技能的学习要求哦！",
         "62":"找不到符合条件的军团!",
         "63":"未冷却",
         "64":"已经邀请过该玩家",
         "65":"军团人没满",
         "66":"军团活动结束",
         "71":"该名字已被其他军团修改使用。"
      };
      
      public function CorpsResult()
      {
         super();
      }
   }
}

