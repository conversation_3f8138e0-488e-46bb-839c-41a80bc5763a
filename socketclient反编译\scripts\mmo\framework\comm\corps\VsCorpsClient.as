package mmo.framework.comm.corps
{
   import com.hexagonstar.util.debug.Debug;
   import flash.events.Event;
   import flash.events.IEventDispatcher;
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.framework.comm.SocketManager;
   
   public class VsCorpsClient
   {
      public static const CMD_INIT_PROPERTY:String = "56_31";
      
      public static const CMD_SIGN_UP_STATE:* = "65_1_1";
      
      public static const CMD_SIGN_UP:* = "65_1_2";
      
      public static const CMD_GET_RANK_NEW:String = "65_1_7";
      
      public static const CMD_GET_READY_ROOM:* = "65_2_1";
      
      public static const CMD_READY_IN_READY_ROOM:* = "65_2_2";
      
      public static const CMD_GAME_STATE:* = "65_2_3";
      
      public static const CMD_AUTO_MATCH:* = "65_2_4";
      
      public static const NOTIFY_ROOM_CREATED:* = "65_2_5";
      
      public static const CMD_FINISH:* = "65_2_6";
      
      public static const CMD_ARMY_FINISH:* = "65_2_7";
      
      public static const CMD_READY:* = "65_2_8";
      
      public static const CMD_RERUEN_REAY_ROOM:* = "65_2_9";
      
      public static const CMD_1V1_COUNT_DOWN:* = "65_2_10";
      
      public static const CMD_GET_READYROOM_CHARACTER_STATE:* = "65_2_11";
      
      public static const CMD_SEND_MATCH_RESULT:* = "65_2_12";
      
      public static const CMD_GET_WAR_ZONE_REGISTER_INFO:* = "65_3_1";
      
      public static const CMD_WAR_ZONE_REGISTER:* = "65_3_2";
      
      public static const CMD_VIEW_WAR_ZONE_REGISTER_ARMY:* = "65_3_3";
      
      public static const CMD_GET_COMPETE_STATE:* = "63_3_4";
      
      public static const CMD_CHOICE_MEMBER:* = "65_3_5";
      
      public static const CMD_GET_BATTLE_SERVER_AND_ROOM:* = "65_3_6";
      
      public static const CMD_GET_WZ_ROUND_RS:* = "65_3_7";
      
      public static const CMD_HOT_ARMY:* = "65_3_8";
      
      public static const CMD_GET_ROUND_ARMY_INFO:* = "65_3_9";
      
      public static const CMD_GET_ROUND_SUPPORT_INFO:* = "65_3_10";
      
      public static const CMD_SUPPORT_ARMY:* = "65_3_11";
      
      public static const CMD_GET_ARMY_ALL_RESULT:* = "65_3_12";
      
      public static const CMD_GET_BATTLE_MEMBERS:* = "65_3_13";
      
      public static const CMD_GET_GIFTBAG:* = "65_3_14";
      
      public static const CMD_GET_ALL_CHAMPIOM_VIEW:* = "65_3_15";
      
      private static var _instance:VsCorpsClient = null;
      
      private static const EXTENSION_NAME:String = ExtMap.ArmyExtention;
      
      public var canJoinLastMatch:Boolean;
      
      private var _saveData:Object = {};
      
      private var _arrRoundInfo:Array;
      
      public function VsCorpsClient()
      {
         super();
         SocketClient.instance.addEventListener(CMD_GET_WAR_ZONE_REGISTER_INFO,this.onGetInfoRspn);
         SocketClient.instance.addEventListener(CMD_GET_WZ_ROUND_RS,this.onGetRoundInfo);
      }
      
      public static function get instance() : VsCorpsClient
      {
         if(_instance == null)
         {
            _instance = new VsCorpsClient();
         }
         return _instance;
      }
      
      public function getGiftBag(param1:int, param2:Function) : void
      {
         var giftBagId:int = param1;
         var callBack:Function = param2;
         this.add(SocketClient.instance,CMD_GET_GIFTBAG,function(param1:Event):void
         {
            callBack.apply(null,[param1]);
         },false);
         this.sendXtMessage(CMD_GET_GIFTBAG,{"gid":giftBagId});
      }
      
      public function getRankData(param1:int, param2:Function, param3:int = 8, param4:int = 1) : void
      {
         var page:int = param1;
         var callBack:Function = param2;
         var PerNum:int = param3;
         var type:int = param4;
         this.add(SocketClient.instance,CMD_GET_RANK_NEW,function(param1:SocketClientEvent):void
         {
            callBack.apply(null,[param1]);
         },false);
         this.sendXtMessage(CMD_GET_RANK_NEW,{
            "type":1,
            "st":(page - 1) * PerNum,
            "end":page * PerNum - 1
         });
      }
      
      private function onGetRoundInfo(param1:SocketClientEvent) : void
      {
         this._arrRoundInfo = param1.params.res;
         Debug.traceObj(this._arrRoundInfo);
         SocketClient.instance.removeEventListener(CMD_GET_WZ_ROUND_RS,this.onGetRoundInfo);
      }
      
      public function getViewDataByWinZoneId(param1:int) : Object
      {
         var _loc2_:Object = null;
         var _loc3_:String = null;
         var _loc4_:Array = null;
         var _loc5_:int = 0;
         var _loc6_:String = null;
         var _loc7_:int = 0;
         for each(_loc2_ in this._arrRoundInfo)
         {
            _loc3_ = _loc2_.cs;
            _loc4_ = _loc3_.split("*");
            _loc5_ = parseInt(_loc4_[0]);
            _loc6_ = Boolean(_loc4_[1]) ? _loc4_[1] : "<b><font color=\'#0000FF\'>参赛军团弃权</font></b>";
            _loc7_ = parseInt(_loc4_[8]);
            if(_loc7_ == param1)
            {
               return {
                  "armyID":_loc5_,
                  "armyName":_loc6_
               };
            }
         }
         return null;
      }
      
      public function getCorpsMatchInfoByArmyId(param1:int, param2:Function) : void
      {
         var dataKey:String = null;
         var armyId:int = param1;
         var callBack:Function = param2;
         dataKey = CMD_GET_ARMY_ALL_RESULT + "@" + armyId;
         if(Boolean(this._saveData[dataKey]))
         {
            callBack.apply(null,[this._saveData[dataKey]]);
            return;
         }
         this.add(SocketClient.instance,CMD_GET_ARMY_ALL_RESULT,function(param1:Event):void
         {
            callBack.apply(null,[param1]);
            _saveData[dataKey] = param1;
         },false,int.MAX_VALUE);
         this.sendXtMessage(CMD_GET_ARMY_ALL_RESULT,{"ai":armyId});
      }
      
      public function getAllChampionView(param1:Function) : void
      {
         var dataKey:String = null;
         var callBack:Function = param1;
         dataKey = CMD_GET_ALL_CHAMPIOM_VIEW;
         if(Boolean(this._saveData[dataKey]))
         {
            callBack.apply(null,[this._saveData[dataKey]]);
            return;
         }
         this.add(SocketClient.instance,CMD_GET_ALL_CHAMPIOM_VIEW,function(param1:Event):void
         {
            callBack.apply(null,[param1]);
            _saveData[dataKey] = param1;
         },false,int.MAX_VALUE);
         this.sendXtMessage(CMD_GET_ALL_CHAMPIOM_VIEW,{});
      }
      
      public function getCorpsMatchBattleMemberByArmyId(param1:int, param2:Function) : *
      {
         var dataKey:String = null;
         var armyId:int = param1;
         var callBack:Function = param2;
         dataKey = CMD_GET_BATTLE_MEMBERS + "@" + armyId;
         if(Boolean(this._saveData[dataKey]))
         {
            callBack.apply(null,[this._saveData[dataKey]]);
            return;
         }
         this.add(SocketClient.instance,CMD_GET_BATTLE_MEMBERS,function(param1:Event):void
         {
            callBack.apply(null,[param1]);
            _saveData[dataKey] = param1;
         },false,int.MAX_VALUE);
         this.sendXtMessage(CMD_GET_BATTLE_MEMBERS,{"ai":armyId});
      }
      
      public function supportArmy(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(CMD_SUPPORT_ARMY,{
            "aid":param1,
            "rd":param2,
            "my":param3
         });
      }
      
      public function getRoundInfo(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_ROUND_ARMY_INFO,{"dt":param1});
      }
      
      public function getRoundSupportInfo(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_ROUND_SUPPORT_INFO,{"dt":param1});
      }
      
      public function getHotCorps(param1:int) : void
      {
         this.sendXtMessage(CMD_HOT_ARMY,{"rk":param1});
      }
      
      public function getWarZoneResult(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_GET_WZ_ROUND_RS,{
            "wid":param1,
            "rd":param2
         });
      }
      
      public function getWarZoneResultByRoundId(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_WZ_ROUND_RS,{"rd":param1});
      }
      
      private function onGetInfoRspn(param1:SocketClientEvent) : void
      {
         var _loc2_:int = parseInt(param1.params.ai);
         this.canJoinLastMatch = _loc2_ != -1;
      }
      
      public function getWarZoneRegisterInfo() : void
      {
         this.sendXtMessage(CMD_GET_WAR_ZONE_REGISTER_INFO,{});
      }
      
      public function registerWarZone(param1:int) : void
      {
         this.sendXtMessage(CMD_WAR_ZONE_REGISTER,{"wid":param1});
      }
      
      public function getRegisterInfoByWid(param1:int) : void
      {
         this.sendXtMessage(CMD_VIEW_WAR_ZONE_REGISTER_ARMY,{"wid":param1});
      }
      
      public function getSingUpState(param1:int = 0) : void
      {
         this.sendXtMessage(CMD_SIGN_UP_STATE,{"vs":param1});
      }
      
      public function singUp(param1:int = 0) : void
      {
         this.sendXtMessage(CMD_SIGN_UP,{"vs":param1});
      }
      
      public function getCompeteState(param1:int = 0) : void
      {
         this.sendXtMessage(CMD_GET_COMPETE_STATE,{"vs":param1});
      }
      
      public function ChooseMember(param1:String) : void
      {
         this.sendXtMessage(CMD_CHOICE_MEMBER,{"mids":param1});
      }
      
      public function getServerRoom() : void
      {
         this.sendXtMessage(CMD_GET_BATTLE_SERVER_AND_ROOM,{});
      }
      
      public function getReadyRomm() : void
      {
         this.sendXtMessage(CMD_GET_READY_ROOM,{});
      }
      
      public function sendReayForReadyRoom() : void
      {
         this.sendXtMessage(CMD_READY_IN_READY_ROOM,{});
      }
      
      public function sendReadyForRomm() : void
      {
         this.sendXtMessage(CMD_READY,{});
      }
      
      public function returnReadyRoom() : void
      {
         this.sendXtMessage(CMD_RERUEN_REAY_ROOM,{});
      }
      
      public function initProperty() : void
      {
         this.sendXtMessage2(CMD_INIT_PROPERTY,{});
      }
      
      public function getReadyRoomState() : void
      {
         this.sendXtMessage(CMD_GET_READYROOM_CHARACTER_STATE,{});
      }
      
      public function add(param1:IEventDispatcher, param2:String, param3:Function, param4:Boolean = true, param5:int = 0) : void
      {
         var getResult:Function = null;
         var currentTarget:IEventDispatcher = param1;
         var type:String = param2;
         var callBack:Function = param3;
         var lockScreen:Boolean = param4;
         var priority:int = param5;
         getResult = function(param1:Event):void
         {
            currentTarget.removeEventListener(type,getResult);
            if(callBack != null)
            {
               callBack.call(null,param1);
            }
         };
         currentTarget.addEventListener(type,getResult,false,priority);
      }
      
      private function sendXtMessage2(param1:String, param2:Object = null) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(ExtMap.GlobalMatch,param1,param2);
      }
      
      private function sendXtMessage(param1:String, param2:Object = null) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

