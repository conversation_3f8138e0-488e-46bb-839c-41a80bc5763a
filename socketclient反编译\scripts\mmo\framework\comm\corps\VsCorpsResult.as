package mmo.framework.comm.corps
{
   public class VsCorpsResult
   {
      public static var SUCCESS:int = 1;
      
      public static var FAILURE:int = 2;
      
      public static var NOT_SIGNUP_TIME:int = 3;
      
      public static var NOT_ARMY_MEMBER:int = 4;
      
      public static var HAS_SIGNUP:int = 5;
      
      public static var MEMBER_COUNT_NOT_ENOUGH:int = 6;
      
      public static var MAX_ARMY_NUM:int = 7;
      
      public static var DULY_LIMIT:int = 8;
      
      public static var NOT_SIGNUP:int = 9;
      
      public static var FIGHT_MEMBER_LIMIT:int = 10;
      
      public static var LEADER_MUST_FIGHT:int = 11;
      
      public static var ARMY_HAS_NO_TARGET:int = 12;
      
      public static var BATTLE_SERVER_BUSY:int = 13;
      
      public static var NOT_CHOICE_YOU:int = 14;
      
      public static var ZONE_ERROR:int = 15;
      
      public static var ROOM_CREATE_ERROR:int = 16;
      
      public static var GAME_NOT_START:int = 17;
      
      public static var ARMY_NOT_PROMOTION:int = 18;
      
      public static var WAR_ZONE_FULL:int = 19;
      
      public static var RMI_CONNECT_ERROR:int = 20;
      
      public static var ARMY_NOT_EXIST:int = 21;
      
      public static var COMPETE_CLOSE:int = 22;
      
      public static var ARMY_HAS_LOSE:int = 23;
      
      public static var ARMY_HAS_WIN:int = 24;
      
      public static var MONEY_ERROR:int = 25;
      
      public static var MONEY_NOT_ENOUGH:int = 26;
      
      public static var SUPPORT_FULL:int = 27;
      
      public static var HAS_SUPPORT_THIS_ARMY:int = 28;
      
      public static var ARMY_NOT_IN_ROUND:int = 29;
      
      public static const ID2TEXT:Object = {
         "1":"成功",
         "2":"失败",
         "3":"不在报名时间",
         "4":"没有加入军团",
         "5":"军团已经报名了",
         "6":"你的军团人数未满20人喔，不符合报名资格。",
         "7":"报名军团名额已满，不能再报名啦！",
         "8":"只有军团长、副军团长、军团队长可以为军团报名参赛喔，快点通知他们来报名吧！",
         "9":"军团木有报名",
         "10":"出战人数过多",
         "11":"军团长必须出战",
         "12":"恭喜你的军团直接获得胜利。",
         "13":"战斗服务器繁忙",
         "14":"木有被选中出战",
         "15":"木有分配导， 或者进错岛",
         "16":"创建房间失败",
         "17":"比赛未开始",
         "18":"军团未晋级到160强",
         "19":"战区报名名额满了",
         "20":"RMI连接错误",
         "21":"军团不存在 ",
         "22":"整个正式赛已经结束了",
         "23":"军团已经输了比赛",
         "24":"军团已经赢了比赛",
         "25":"下注的金额错误",
         "26":"金币不足",
         "27":"每天比赛只能支持5个军团喔！",
         "28":"已经支持了该军团",
         "29":"支持的军团木有晋级到本轮",
         "31":"要在比赛当天才能选择出战成员。",
         "32":"要在比赛当天14:30分之前才能支持军团哦。"
      };
      
      public function VsCorpsResult()
      {
         super();
      }
   }
}

