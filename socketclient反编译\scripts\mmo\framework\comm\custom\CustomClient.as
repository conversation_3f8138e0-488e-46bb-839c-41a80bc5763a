package mmo.framework.comm.custom
{
   import mmo.framework.comm.SocketClient;
   
   public class CustomClient
   {
      private static var _instance:CustomClient;
      
      private static const EXTENSION_NAME:* = "CustomExtension";
      
      public static const cmdLoadQuickLaunch:* = "11_0";
      
      public static const cmdUpdateQuickLaunchHotkey:* = "11_1";
      
      public static const cmdUpdateQuickLaunchExtData:* = "11_2";
      
      public function CustomClient()
      {
         super();
      }
      
      public static function get instance() : CustomClient
      {
         if(_instance == null)
         {
            _instance = new CustomClient();
         }
         return _instance;
      }
      
      public function updateQuickLaunchExtData(param1:String) : void
      {
         this.sendXtMessage(cmdUpdateQuickLaunchExtData,{"ed":param1});
      }
      
      public function updateQuickLaunchHotkey(param1:String) : void
      {
         this.sendXtMessage(cmdUpdateQuickLaunchHotkey,{"hk":param1});
      }
      
      public function loadQuickLaunch() : void
      {
         this.sendXtMessage(cmdLoadQuickLaunch,{});
      }
      
      final protected function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

