package mmo.framework.comm.data
{
   import flash.utils.Dictionary;
   import mmo.socketserver.data.User;
   
   public class UserPoint extends Dictionary
   {
      public var x:int;
      
      public var y:int;
      
      public var pointType:int;
      
      public var walkSpeed:int;
      
      public var activeRoom:String;
      
      public var extData:String;
      
      public function UserPoint(param1:int, param2:int, param3:int = 1, param4:int = 0, param5:String = "", param6:String = "")
      {
         super();
         this.x = param1;
         this.y = param2;
         this.pointType = param3;
         this.walkSpeed = param4;
         this.activeRoom = param5;
         this.extData = param6;
      }
      
      public static function getUserPoint(param1:User) : UserPoint
      {
         var _loc2_:String = param1.getVariable("pt");
         if(_loc2_ == null)
         {
            return null;
         }
         var _loc3_:Array = _loc2_.split(",");
         return new UserPoint(_loc3_[0],_loc3_[1],_loc3_[2],_loc3_[3],_loc3_[4],_loc3_[5]);
      }
      
      public function toString() : String
      {
         return this.x + "," + this.y + "," + this.pointType + "," + this.walkSpeed + "," + this.activeRoom + "," + this.extData;
      }
   }
}

