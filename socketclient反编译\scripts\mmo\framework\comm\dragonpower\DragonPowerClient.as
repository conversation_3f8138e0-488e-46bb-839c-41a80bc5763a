package mmo.framework.comm.dragonpower
{
   import flash.utils.getDefinitionByName;
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class DragonPowerClient
   {
      public static const CMD_GET_INFO:String = "90_1";
      
      public static const CMD_PRACTISE:String = "90_2";
      
      public static const CMD_SVAE_CANCEL:String = "90_3";
      
      public static const CMD_UPDRAGE_DRAGON:String = "90_4";
      
      public static const CMD_GET_TASK_INFO:String = "90_5";
      
      public static const CMD_GET_TASK_BONUS:String = "90_6";
      
      public static const NOTIFY_GET_ITEM:String = "90_7";
      
      public function DragonPowerClient()
      {
         super();
      }
      
      public static function getInfo(param1:Function) : void
      {
         sendXTMessage(CMD_GET_INFO,{},param1);
      }
      
      public static function practise(param1:int, param2:Function) : void
      {
         sendXTMessage(CMD_PRACTISE,{"type":param1},param2);
      }
      
      public static function savePractice(param1:Function) : void
      {
         sendXTMessage(CMD_SVAE_CANCEL,{"type":1},param1);
      }
      
      public static function cancelPractice(param1:Function) : void
      {
         sendXTMessage(CMD_SVAE_CANCEL,{"type":0},param1);
      }
      
      public static function upgrade(param1:int, param2:Function) : void
      {
         sendXTMessage(CMD_UPDRAGE_DRAGON,{"num":param1},param2);
      }
      
      public static function getTaskInfo(param1:Function, param2:Boolean = true) : void
      {
         sendXTMessage(CMD_GET_TASK_INFO,{},param1,param2);
      }
      
      public static function getTaskBonus(param1:int, param2:Function) : void
      {
         sendXTMessage(CMD_GET_TASK_BONUS,{"id":param1},param2);
      }
      
      private static function sendXTMessage(param1:String, param2:Object = null, param3:Function = null, param4:Boolean = true) : void
      {
         var listener:Class;
         var cmd:String = param1;
         var params:Object = param2;
         var callback:Function = param3;
         var isLockScreen:Boolean = param4;
         if(callback == null)
         {
            callback = function temp():*
            {
            };
         }
         listener = getDefinitionByName("mmo.common.utils.event.OnceEventListener") as Class;
         listener["add"](SocketClient.instance,cmd,callback,isLockScreen);
         SocketClient.instance.sendXtMessage(ExtMap.DragonPowerExtension,cmd,params);
      }
   }
}

