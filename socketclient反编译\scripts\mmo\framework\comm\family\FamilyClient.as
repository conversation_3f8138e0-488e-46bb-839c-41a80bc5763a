package mmo.framework.comm.family
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class FamilyClient
   {
      private static var _instance:FamilyClient = null;
      
      private static const EXTENSION_NAME:String = ExtMap.FamilyExtension;
      
      public static const CMD_CHECK_FAMILY_NAME:String = "43_0";
      
      public static const CMD_CREATE_FAMILY:String = "43_1";
      
      public static const CMD_GET_MY_FAMILY_INFO:String = "43_2";
      
      public static const CMD_LOAD_MY_FAMILY:String = "43_3";
      
      public static const CMD_GET_OUT:String = "43_4";
      
      public static const CMD_CHANGE_DUTY:String = "43_5";
      
      public static const CMD_CHANGE_INFORM:String = "43_6";
      
      public static const CMD_VIEW_ALL_MEMBER:String = "43_7";
      
      public static const CMD_GET_ALL_FAMILY_WITH_LIKE_NAME:String = "43_8";
      
      public static const CMD_SEND_INVITE_JOIN:String = "43_9";
      
      public static const CMD_HANDLE_INVITE_JOIN:String = "43_10";
      
      public static const CMD_SEND_REQUEST_JOIN:String = "43_11";
      
      public static const CMD_HANDLE_REQUEST_JOIN:String = "43_12";
      
      public static const CMD_VIEW_OTHER_FAMILY_INFO:String = "43_13";
      
      public static const CMD_GET_FAMILY_BY_FAMILYNAME:String = "43_14";
      
      public static const CMD_CHANGE_RESUME:String = "43_15";
      
      public static const CMD_GET_FAMILY_EVENT:String = "43_16";
      
      public static const CMD_WATER_EXP_TREE:String = "43_17";
      
      public static const CMD_GET_RECOMMAND_FAMILY:String = "43_18";
      
      public static const CMD_GET_NEW_FAMILY:String = "43_19";
      
      public static const CMD_GET_FAMILY_ROOM_ID:String = "43_21";
      
      public static const CMD_GET_RANDOM_CHARS:String = "43_20";
      
      public static const CMD_GET_FAMILY_ROOM_INFO:String = "43_22";
      
      public static const CMD_DONATE_STUFF:String = "43_23";
      
      public static const CMD_ALLOT_STUFF:String = "43_24";
      
      public static const CMD_STORAGE_STUFF:String = "43_25";
      
      public static const CMD_NOTIFY_ROOM_INFO:String = "43_26";
      
      public static const CMD_NOTIFY_CHAR_FAMILY_SCORE:String = "43_40";
      
      public static const CMD_GET_FAMILY_BOSS_ROOMID:String = "43_60";
      
      public static const CMD_REFRESH_BOSS:String = "43_61";
      
      public static const CMD_TRY_REFRESH_BOSS:String = "43_62";
      
      public static const CMD_UPDATE_CHALLENGE_INFO:String = "43_63";
      
      public static const CMD_GET_MY_STAR_INFO:String = "43_70";
      
      public static const CMD_GET_ALL_MEMS_STAR:String = "43_71";
      
      public static const CMD_CHANGE_STAR:String = "43_72";
      
      public static const CMD_GET_STAR_BONUS_STATE:String = "43_73";
      
      public static const CMD_GET_RANK_BNOUS:String = "43_74";
      
      public static const CMD_GET_STAR_BONUS:String = "43_75";
      
      public static const CMD_GET_FAMILY_SCORE_AND_RANK:String = "43_82";
      
      public static const CMD_GET_RANK_INFO:String = "43_83";
      
      public static const CMD_FAMILY_OUT_INFO:String = "43_84";
      
      public static const CMD_GET_FAMILY_SCORE_AND_RANK_DYNAMIC:String = "43_85";
      
      public function FamilyClient()
      {
         super();
      }
      
      public static function get instance() : FamilyClient
      {
         if(_instance == null)
         {
            _instance = new FamilyClient();
         }
         return _instance;
      }
      
      public function reqStarBonus(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_STAR_BONUS,{"id":param1});
      }
      
      public function reqStarRankBonus() : void
      {
         this.sendXtMessage(CMD_GET_RANK_BNOUS,{});
      }
      
      public function reqStarRewardState(param1:int) : *
      {
         this.sendXtMessage(CMD_GET_STAR_BONUS_STATE,{});
      }
      
      public function reqChangeStar(param1:int) : void
      {
         this.sendXtMessage(CMD_CHANGE_STAR,{"in":param1});
      }
      
      public function reqStarRankListInfo() : void
      {
         this.sendXtMessage(CMD_GET_ALL_MEMS_STAR,{});
      }
      
      public function reqStarInfo() : void
      {
         this.sendXtMessage(CMD_GET_MY_STAR_INFO,{});
      }
      
      public function reqRandomRecommend() : void
      {
         this.sendXtMessage(CMD_GET_RANDOM_CHARS,{});
      }
      
      public function sendReflashBoss(param1:int) : void
      {
         this.sendXtMessage(CMD_REFRESH_BOSS,{"rId":param1});
      }
      
      public function sendEnterFamilyBossRoom() : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_BOSS_ROOMID,{});
      }
      
      public function send2BuildingByMember(param1:int, param2:int, param3:int, param4:int, param5:int) : void
      {
         this.sendXtMessage(CMD_DONATE_STUFF,{
            "tp":param1,
            "gl":param2,
            "wd":param4,
            "sn":param3,
            "ir":param5
         });
      }
      
      public function send2BuildingByChief(param1:int, param2:int, param3:int, param4:int, param5:int) : void
      {
         this.sendXtMessage(CMD_ALLOT_STUFF,{
            "tp":param1,
            "gl":param2,
            "wd":param4,
            "sn":param3,
            "ir":param5
         });
      }
      
      public function send2Storage(param1:int, param2:int, param3:int, param4:int) : void
      {
         this.sendXtMessage(CMD_STORAGE_STUFF,{
            "gl":param1,
            "wd":param3,
            "sn":param2,
            "ir":param4
         });
      }
      
      public function reqFamilyFieldRoomId(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_ROOM_ID,{"n":param1});
      }
      
      public function reqFamilyFieldInfo(param1:String) : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_ROOM_INFO,{"n":param1});
      }
      
      public function reqWater() : void
      {
         this.sendXtMessage(CMD_WATER_EXP_TREE,{});
      }
      
      public function reqEditStatement(param1:String, param2:String) : void
      {
         this.sendXtMessage(CMD_CHANGE_INFORM,{
            "info":param1,
            "dao":param2
         });
      }
      
      public function reqEditIntroduce(param1:String) : void
      {
         this.sendXtMessage(CMD_CHANGE_RESUME,{"info":param1});
      }
      
      public function sendApplyResult(param1:String, param2:String, param3:Boolean) : void
      {
         if(param2 == null || param2.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_HANDLE_REQUEST_JOIN,{
            "nm":param1,
            "fn":param2,
            "ag":param3
         });
      }
      
      public function reqChangeDuty(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_CHANGE_DUTY,{
            "dt":param1,
            "ci":param2
         });
      }
      
      public function reqFamilyInfoByName(param1:String) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_GET_FAMILY_BY_FAMILYNAME,{"fm":param1});
      }
      
      public function reqQuitFamily() : void
      {
         this.sendXtMessage(CMD_GET_OUT,{});
      }
      
      public function reqKickMember(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_OUT,{"ci":param1});
      }
      
      public function getRecommand() : void
      {
         this.sendXtMessage(CMD_GET_RECOMMAND_FAMILY,{});
      }
      
      public function getNewFamilies() : void
      {
         this.sendXtMessage(CMD_GET_NEW_FAMILY,{});
      }
      
      public function inviteMemberJoinFamily(param1:String) : void
      {
         this.sendXtMessage(CMD_SEND_INVITE_JOIN,{"nm":param1});
      }
      
      public function sendAgreeJoinFamily(param1:String, param2:String, param3:Boolean = true) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_HANDLE_INVITE_JOIN,{
            "fn":param1,
            "nm":param2,
            "ag":param3
         });
      }
      
      public function reqJoinFamily(param1:String, param2:int) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_SEND_REQUEST_JOIN,{
            "fn":param1,
            "fi":param2
         });
      }
      
      public function reqFamilyListByName(param1:String) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_GET_ALL_FAMILY_WITH_LIKE_NAME,{"ln":param1});
      }
      
      public function reqFamilyList() : void
      {
         this.sendXtMessage(CMD_GET_MY_FAMILY_INFO,{});
      }
      
      public function reqMyFamilyInfoWithoutMembers() : void
      {
         this.sendXtMessage(CMD_LOAD_MY_FAMILY,{});
      }
      
      public function reqMyFamilyInfo() : void
      {
         this.sendXtMessage(CMD_GET_MY_FAMILY_INFO,{});
      }
      
      public function reqFamilyInfoByID(param1:int) : void
      {
      }
      
      public function reqCheckFamilyName(param1:String) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_CHECK_FAMILY_NAME,{"fn":param1});
      }
      
      public function reqCreateFamily(param1:String, param2:String) : void
      {
         if(param1 == null || param1.length == 0)
         {
            return;
         }
         this.sendXtMessage(CMD_CREATE_FAMILY,{
            "fn":param1,
            "info":param2
         });
      }
      
      public function reqGetFamilySimpleInfoByCID(param1:int) : void
      {
         this.sendXtMessage(CMD_VIEW_OTHER_FAMILY_INFO,{"ci":param1});
      }
      
      public function reqGetFamilyEvent() : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_EVENT);
      }
      
      public function reqTryBossReflash(param1:int) : void
      {
         this.sendXtMessage(CMD_TRY_REFRESH_BOSS,{"rId":param1});
      }
      
      public function reqEditBossFamily(param1:String) : void
      {
         this.sendXtMessage(CMD_UPDATE_CHALLENGE_INFO,{"info":param1});
      }
      
      public function getMyRank(param1:String = null) : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_SCORE_AND_RANK,{"sis":param1});
      }
      
      public function getMyRankDynamic(param1:String = null) : void
      {
         this.sendXtMessage(CMD_GET_FAMILY_SCORE_AND_RANK_DYNAMIC,{"sis":param1});
      }
      
      public function getRankInfo(param1:int, param2:int) : void
      {
         this.sendXtMessage(CMD_GET_RANK_INFO,{
            "si":param1,
            "p":param2
         });
      }
      
      public function getMyFamilyOutInfo() : void
      {
         this.sendXtMessage(CMD_FAMILY_OUT_INFO,{});
      }
      
      private function sendXtMessage(param1:String, param2:Object = null) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

