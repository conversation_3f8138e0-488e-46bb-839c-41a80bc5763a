package mmo.framework.comm.fb
{
   import mmo.framework.comm.SocketClient;
   
   public class FbClient
   {
      private static var _instance:FbClient;
      
      private static const EXTENSION_NAME:String = "FBExtension";
      
      public static const cmdCreateAndJoinFB:String = "15_0";
      
      public static const cmdLeaveFB:String = "15_1";
      
      public static const cmdGetFBHistoryRecord:String = "15_2";
      
      public static const cmdRefreshCreepManual:String = "15_3";
      
      public static const cmdFinishFB:String = "15_4";
      
      public static const cmdViewFBLevel:String = "15_5";
      
      public static const cmdOpenBonusBox:String = "15_6";
      
      public static const cmdGetAllFbPassRecord:String = "15_7";
      
      public static const cmdCreepDead:String = "15_11";
      
      public static const cmdSkillDamageToCreep:String = "15_12";
      
      public static const cmdGetDamageRange:String = "15_13";
      
      public static const cmdChangeCharacterPoint:String = "15_14";
      
      public static const cmdBuffDamageToCreep:String = "15_16";
      
      public static const cmdGetFbDoubleAwardList:String = "15_17";
      
      public static const cmdGetFbLeftEnterTimes:String = "15_18";
      
      public function FbClient()
      {
         super();
      }
      
      public static function get instance() : FbClient
      {
         if(_instance == null)
         {
            _instance = new FbClient();
         }
         return _instance;
      }
      
      public function creatAndJoinFb(param1:int, param2:int, param3:Boolean = false) : void
      {
         this.sendXtMessage(cmdCreateAndJoinFB,{
            "fbId":param1,
            "fblv":param2,
            "sgc":param3
         });
      }
      
      public function leaveFb(param1:Object = null) : void
      {
         this.sendXtMessage(cmdLeaveFB,param1);
      }
      
      public function tryFinFb(param1:Object = null) : void
      {
         this.sendXtMessage(cmdFinishFB,param1);
      }
      
      public function getFbHistoryRecord(param1:int) : void
      {
         this.sendXtMessage(cmdGetFBHistoryRecord,{"fbId":param1});
      }
      
      public function refreshMonster(param1:int) : void
      {
         this.sendXtMessage(cmdRefreshCreepManual,{"crId":param1});
      }
      
      public function getFbChoosePanelConfig(param1:int) : void
      {
         this.sendXtMessage(cmdViewFBLevel,{"fbId":param1});
      }
      
      public function sendChoiceResult(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(cmdOpenBonusBox,{
            "fc":param1,
            "vc":param2,
            "sc":param3
         });
      }
      
      public function getAllFbRecord() : void
      {
         this.sendXtMessage(cmdGetAllFbPassRecord,null);
      }
      
      public function getDamageRange(param1:Boolean, param2:Array = null) : void
      {
         this.sendXtMessage(cmdGetDamageRange,{
            "a":param1,
            "t":param2
         });
      }
      
      public function sendPointValueChange(param1:int, param2:int) : void
      {
         this.sendXtMessage(cmdChangeCharacterPoint,{
            "p":param1,
            "v":param2
         });
      }
      
      public function sendDamageValue(param1:int, param2:String) : void
      {
         this.sendXtMessage(cmdSkillDamageToCreep,{
            "s":param1,
            "d":param2
         });
      }
      
      public function sendBuffDamageValue(param1:int, param2:String, param3:int) : void
      {
         this.sendXtMessage(cmdBuffDamageToCreep,{
            "c":param1,
            "k":param2,
            "d":param3
         });
      }
      
      public function getFbDoubleAwardList() : void
      {
         this.sendXtMessage(cmdGetFbDoubleAwardList,null);
      }
      
      public function getFbLeftEnterTimes(param1:int, param2:int) : void
      {
         this.sendXtMessage(cmdGetFbLeftEnterTimes,{
            "fbId":param1,
            "level":param2
         });
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

