package mmo.framework.comm.festival
{
   import mmo.framework.comm.SocketClient;
   
   public class FestivalClient
   {
      private static var _instance:FestivalClient;
      
      private static const EXTENSION_NAME:String = "FestivalExtension";
      
      public static const cmdGetProgress:String = "24_0_1";
      
      public static const cmdGetWeekendBuff:String = "24_1";
      
      public static const cmdNotifyWeekendBuff:String = "24_2";
      
      public function FestivalClient()
      {
         super();
      }
      
      public static function get instance() : FestivalClient
      {
         if(_instance == null)
         {
            _instance = new FestivalClient();
         }
         return _instance;
      }
      
      public function getProgress() : void
      {
         this.sendXtMessage(cmdGetProgress,{});
      }
      
      public function checkDragonBlessBuff() : void
      {
         this.sendXtMessage(cmdGetWeekendBuff,{});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

