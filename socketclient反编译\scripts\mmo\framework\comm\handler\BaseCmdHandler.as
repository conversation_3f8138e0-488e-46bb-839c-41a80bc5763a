package mmo.framework.comm.handler
{
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   public class BaseCmdHandler
   {
      protected var _dispatcher:EventDispatcher;
      
      public function BaseCmdHandler(param1:EventDispatcher)
      {
         super();
         this._dispatcher = param1;
      }
      
      protected function dispatchEvent(param1:Event) : *
      {
         this._dispatcher.dispatchEvent(param1);
      }
   }
}

