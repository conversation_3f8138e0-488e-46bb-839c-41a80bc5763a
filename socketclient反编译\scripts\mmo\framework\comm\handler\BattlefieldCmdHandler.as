package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class BattlefieldCmdHandler extends BaseCmdHandler
   {
      private static const TYPE_PLAYER:int = 1;
      
      private static const TYPE_MONSTER:int = 2;
      
      public function BattlefieldCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleMyCharJoinBF(param1:SSEvent) : void
      {
         var _loc6_:Object = null;
         var _loc7_:int = 0;
         var _loc2_:Array = param1.params.dataObj.cl;
         var _loc3_:Object = {};
         var _loc4_:Array = new Array();
         var _loc5_:Array = new Array();
         _loc3_.bi = param1.params.dataObj.bi;
         for each(_loc6_ in _loc2_)
         {
            _loc7_ = int(_loc6_.bot);
            if(_loc7_ == TYPE_PLAYER)
            {
               _loc4_.push(_loc6_);
            }
            else if(_loc7_ == TYPE_MONSTER)
            {
               _loc5_.push(_loc6_);
            }
         }
         _loc3_.cl = _loc4_;
         _loc3_.ml = _loc5_;
         this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onMyCharJoinBattleField,_loc3_));
      }
      
      public function handleOtherCharJoinBF(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:uint = uint(param1.params.dataObj.bot);
         if(_loc3_ == TYPE_PLAYER)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onOtherCharJoinBattleField,_loc2_));
         }
         else if(_loc3_ == TYPE_MONSTER)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onMonsterJoinBattleField,_loc2_));
         }
      }
      
      public function handleMyCharLeaveBF(param1:SSEvent) : void
      {
         this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onMyCharLeaveBattleField,param1.params.dataObj));
      }
      
      public function handleOtherCharLeaveBF(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:uint = uint(_loc2_.bot);
         if(_loc3_ == TYPE_PLAYER)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onOtherCharLeaveBattleField,_loc2_));
         }
         else if(_loc3_ == TYPE_MONSTER)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onMonsterLeaveBattleField,_loc2_));
         }
      }
   }
}

