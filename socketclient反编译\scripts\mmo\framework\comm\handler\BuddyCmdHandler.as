package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.BuddyClient;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class BuddyCmd<PERSON>and<PERSON> extends BaseCmdHandler
   {
      public function BuddyCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleUpdateBuddyGroup(param1:SSEvent) : void
      {
         BuddyClient.instance.updateBuddyGroup(param1.params.dataObj.bn,param1.params.dataObj.tg);
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.UPDATE_BUDDY_GROUP,param1.params.dataObj);
         this.dispatchEvent(_loc2_);
      }
      
      public function handleCheckUserExist(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onCheckUserExist,param1.params);
         this.dispatchEvent(_loc2_);
      }
      
      public function handleLocateBuddy(param1:SSEvent) : *
      {
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onLocateBuddy,_loc2_);
         this.dispatchEvent(_loc3_);
      }
   }
}

