package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.Commands;
   
   public class CmdHandlerMap
   {
      public function CmdHandlerMap()
      {
         super();
      }
      
      public static function getHandlerMap(param1:EventDispatcher) : Object
      {
         var _loc2_:MessageCmdHandler = new MessageCmdHandler(param1);
         var _loc3_:BattlefieldCmdHandler = new BattlefieldCmdHandler(param1);
         var _loc4_:BuddyCmdHandler = new BuddyCmdHandler(param1);
         var _loc5_:MessageCmdHandler = new MessageCmdHandler(param1);
         var _loc6_:MonsterCmdHandler = new MonsterCmdHandler(param1);
         var _loc7_:PlayerCmdHandler = new PlayerCmdHandler(param1);
         var _loc8_:ScoreCmdHandler = new ScoreCmdHandler(param1);
         var _loc9_:CustomCmdHandler = new CustomCmdHandler(param1);
         var _loc10_:FlyPetCmdHandler = new FlyPetCmdHandler(param1);
         var _loc11_:Object = {};
         _loc11_[Commands.customCommand] = _loc9_.handleCustomCommand;
         _loc11_[Commands.cmdSysMsg] = _loc2_.handleSysMsg;
         _loc11_[Commands.CHECK_USER_EXIST] = _loc4_.handleCheckUserExist;
         _loc11_[Commands.UPDATE_BUDDY_GROUP] = _loc4_.handleUpdateBuddyGroup;
         _loc11_[Commands.onLocateBuddy] = _loc4_.handleLocateBuddy;
         _loc11_[Commands.cmdScoreAmountNotify] = _loc8_.handleScoreNotify;
         _loc11_[Commands.cmdWeeklyScoreNotify] = _loc8_.handleWeeklyScoreNotify;
         _loc11_[Commands.cmdMoneyLimitNotify] = _loc8_.handleMoneyLimitedNotify;
         _loc11_[Commands.cmdMyCharJoinBF] = _loc3_.handleMyCharJoinBF;
         _loc11_[Commands.cmdOtherCharJoinBF] = _loc3_.handleOtherCharJoinBF;
         _loc11_[Commands.cmdMyCharLeaveBF] = _loc3_.handleMyCharLeaveBF;
         _loc11_[Commands.cmdOtherCharLeaveBF] = _loc3_.handleOtherCharLeaveBF;
         _loc11_[Commands.cmdPlayerVecterUpdate] = _loc7_.handlePlayerVectorUpdate;
         _loc11_[Commands.cmdPlayerJump] = _loc7_.handlePlayerJump;
         _loc11_[Commands.cmdPlayerDead] = _loc7_.handlePlayerDead;
         _loc11_[Commands.cmdPlayerRevive] = _loc7_.handlePlayerRevive;
         _loc11_[Commands.cmdPlayerVectorUpdateBinary] = _loc7_.handlePlayerVectorUpdateBinary;
         _loc11_[Commands.cmdPlayerJumpBinary] = _loc7_.handlePlayerJumpBinary;
         _loc11_[Commands.cmdMonsterMove] = _loc6_.handleMonsterMove;
         _loc11_[Commands.cmdMonsterStop] = _loc6_.handleMonsterStop;
         _loc11_[Commands.cmdMonsterStand] = _loc6_.handleMonsterStand;
         _loc11_[Commands.cmdFlyPetMove] = _loc10_.handleFlyPetMove;
         _loc11_[Commands.cmdFlyPetStop] = _loc10_.handleFlyPetStop;
         _loc11_[Commands.cmdFlyPetStand] = _loc10_.handleFlyPetStand;
         return _loc11_;
      }
   }
}

