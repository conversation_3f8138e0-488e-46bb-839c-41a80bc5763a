package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class CustomCmdHandler extends BaseCmdHandler
   {
      public function CustomCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleCustomCommand(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onCustomCommand,param1.params);
         this.dispatchEvent(_loc2_);
      }
   }
}

