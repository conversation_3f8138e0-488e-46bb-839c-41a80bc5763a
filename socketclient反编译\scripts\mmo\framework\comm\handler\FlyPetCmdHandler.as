package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class FlyPetCmdHandler extends BaseCmdHandler
   {
      public function FlyPetCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleFlyPetMove(param1:SSEvent) : void
      {
         var _loc2_:Object = new Object();
         var _loc3_:Object = param1.params.dataObj;
         _loc2_.ci = _loc3_.ci;
         _loc2_.s = _loc3_.s;
         _loc2_.bt = _loc3_.bt;
         _loc2_.x = _loc3_.x;
         _loc2_.y = _loc3_.y;
         var _loc4_:String = String(_loc3_.p);
         if(_loc4_ != "")
         {
            _loc2_.pa = _loc4_.split("#");
         }
         var _loc5_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onFlyPetMove,_loc2_);
         this.dispatchEvent(_loc5_);
      }
      
      public function handleFlyPetStop(param1:SSEvent) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.ci = param1.params.dataObj.ci;
         _loc2_.bi = param1.params.dataObj.bi;
         _loc2_.x = param1.params.dataObj.x;
         _loc2_.y = param1.params.dataObj.y;
         var _loc3_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onFlyPetStop,_loc2_);
         this.dispatchEvent(_loc3_);
      }
      
      public function handleFlyPetStand(param1:SSEvent) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.ci = param1.params.dataObj.ci;
         _loc2_.bi = param1.params.dataObj.bi;
         var _loc3_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onFlyPetStand,_loc2_);
         this.dispatchEvent(_loc3_);
      }
   }
}

