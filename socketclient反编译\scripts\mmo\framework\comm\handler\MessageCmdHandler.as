package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class MessageCmdHandler extends BaseCmdHandler
   {
      public function MessageCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleSysMsg(param1:SSEvent) : void
      {
         this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onSysMsg,param1.params.dataObj));
      }
   }
}

