package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import flash.utils.ByteArray;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.framework.comm.data.PositionChangeType;
   import mmo.socketserver.SSEvent;
   
   public class PlayerCmdHandler extends BaseCmdHandler
   {
      private const TYPEID_2_NAME:Object = {
         "1":"玩家",
         "2":"怪物",
         "3":"物品",
         "4":"宠物",
         "5":"飞行兽",
         "6":"建筑"
      };
      
      public function PlayerCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handlePlayerVectorUpdate(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = null;
         var _loc3_:Object = null;
         var _loc5_:int = 0;
         var _loc4_:int = int(param1.params.dataObj.ci);
         if(_loc4_ < 0)
         {
            _loc5_ = int(param1.params.dataObj.t);
            if(_loc5_ == PositionChangeType.ChangeDirection)
            {
               _loc3_ = new Object();
               _loc3_.ci = int(param1.params.dataObj.ci);
               _loc3_.bi = int(param1.params.dataObj.bi);
               _loc3_.d = int(param1.params.dataObj.d);
               _loc2_ = new SocketClientEvent(SocketClientEvent.onMonsterChangeDirection,_loc3_);
               this.dispatchEvent(_loc2_);
            }
         }
         else
         {
            _loc3_ = param1.params.dataObj;
            _loc2_ = new SocketClientEvent(SocketClientEvent.onCharacterVectorUpdate,_loc3_);
            this.dispatchEvent(_loc2_);
         }
      }
      
      public function handlePlayerVectorUpdateBinary(param1:SSEvent) : void
      {
         var _loc2_:ByteArray = param1.params.dataObj as ByteArray;
         var _loc3_:int = _loc2_.readInt();
         _loc2_.readInt();
         var _loc4_:int = _loc2_.readInt();
         var _loc5_:int = _loc2_.readShort();
         var _loc6_:int = _loc2_.readShort();
         var _loc7_:int = _loc2_.readByte();
         var _loc8_:int = _loc7_ >>> 4;
         var _loc9_:int = _loc7_ & 15;
         var _loc10_:int = _loc2_.readByte();
         var _loc11_:Object = {
            "ci":_loc4_,
            "bi":_loc3_,
            "x":_loc5_,
            "y":_loc6_,
            "d":_loc8_,
            "t":_loc9_,
            "s":_loc10_
         };
         var _loc12_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onCharacterVectorUpdate,_loc11_);
         this.dispatchEvent(_loc12_);
      }
      
      public function handlePlayerJump(param1:SSEvent) : void
      {
         var _loc2_:String = null;
         var _loc3_:int = int(param1.params.dataObj.ci);
         if(_loc3_ < 0)
         {
            _loc2_ = SocketClientEvent.onMonsterJump;
         }
         else
         {
            _loc2_ = SocketClientEvent.onCharacterJump;
         }
         this.dispatchEvent(new SocketClientEvent(_loc2_,param1.params.dataObj));
      }
      
      public function handlePlayerJumpBinary(param1:SSEvent) : void
      {
      }
      
      public function handlePlayerDead(param1:SSEvent) : void
      {
         var _loc6_:Object = null;
         var _loc2_:Array = param1.params.dataObj.dl;
         var _loc3_:Array = new Array();
         var _loc4_:Array = new Array();
         var _loc5_:Array = new Array();
         for each(_loc6_ in _loc2_)
         {
            switch(_loc6_.type)
            {
               case 1:
                  _loc4_.push(_loc6_);
                  break;
               case 2:
                  _loc3_.push(_loc6_);
                  break;
               case 5:
                  _loc5_.push(_loc6_);
                  break;
            }
         }
         if(_loc4_.length > 0)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onCharacterDie,{"dl":_loc4_}));
         }
         if(_loc3_.length > 0)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onMonsterDie,{"dl":_loc3_}));
         }
         if(_loc5_.length > 0)
         {
            this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onFlyPetDie,{"dl":_loc5_}));
         }
      }
      
      public function handlePlayerRevive(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params.dataObj;
         this.dispatchEvent(new SocketClientEvent(SocketClientEvent.onCharacterRevive,_loc2_));
      }
   }
}

