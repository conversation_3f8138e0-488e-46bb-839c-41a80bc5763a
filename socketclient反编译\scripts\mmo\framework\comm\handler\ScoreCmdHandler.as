package mmo.framework.comm.handler
{
   import flash.events.EventDispatcher;
   import mmo.common.user.UserInfo;
   import mmo.framework.comm.Commands;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class ScoreCmdHandler extends BaseCmdHandler
   {
      public function ScoreCmdHandler(param1:EventDispatcher)
      {
         super(param1);
      }
      
      public function handleScoreNotify(param1:SSEvent) : void
      {
         UserInfo.money = int(param1.params.dataObj.bs);
         UserInfo.bindMoney = int(param1.params.dataObj.ps);
         UserInfo.rainbowConch = int(param1.params.dataObj.es);
         UserInfo.honor = int(param1.params.dataObj.hs);
         var _loc2_:SocketClientEvent = new SocketClientEvent(Commands.cmdScoreAmountNotify,param1.params.dataObj);
         this.dispatchEvent(_loc2_);
      }
      
      public function handleWeeklyScoreNotify(param1:SSEvent) : void
      {
         UserInfo.weeklyScores = int(param1.params.dataObj.ws);
         var _loc2_:SocketClientEvent = new SocketClientEvent(Commands.cmdWeeklyScoreNotify,param1.params.dataObj);
         this.dispatchEvent(_loc2_);
      }
      
      public function handleMoneyLimitedNotify(param1:SSEvent) : void
      {
         UserInfo.todayRemainingMoney = int(param1.params.dataObj.rsc);
         var _loc2_:SocketClientEvent = new SocketClientEvent(Commands.cmdMoneyLimitNotify,param1.params.dataObj);
         this.dispatchEvent(_loc2_);
      }
   }
}

