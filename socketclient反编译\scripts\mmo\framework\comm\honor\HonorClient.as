package mmo.framework.comm.honor
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class HonorClient
   {
      private static var _instance:HonorClient;
      
      public static var ViewInfo:String = "71_1";
      
      public static var NotifyGetHonor:String = "71_2";
      
      public static var LoadBuddyHonor:String = "71_3";
      
      public function HonorClient()
      {
         super();
      }
      
      public static function get instance() : HonorClient
      {
         if(_instance == null)
         {
            _instance = new HonorClient();
         }
         return _instance;
      }
      
      public function viewMyHonorInfo() : void
      {
         this.sendXtMessage(ViewInfo,{});
      }
      
      public function viewOtherHonorInfo(param1:int) : void
      {
         this.sendXtMessage(ViewInfo,{"oc":param1});
      }
      
      public function loadBuddyHonor() : void
      {
         this.sendXtMessage(LoadBuddyHonor,{});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.HonorExtension,param1,param2);
      }
   }
}

