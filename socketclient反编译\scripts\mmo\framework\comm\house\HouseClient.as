package mmo.framework.comm.house
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class HouseClient
   {
      public static const CMD_LOAD_ROOM:String = "107_1";
      
      public static const CMD_LOAD_INFO:String = "107_2";
      
      public static const CMD_SAVE_DECORATE:String = "107_3";
      
      public static const CMD_BUY_DECORATE:String = "107_4";
      
      private static const EXTENSION_NAME:String = ExtMap.HouseExtension;
      
      public function HouseClient()
      {
         super();
      }
      
      public static function sendXtMsgWithCallBack(param1:String, param2:Function = null, param3:Object = null) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,param1,param3,param2);
      }
   }
}

