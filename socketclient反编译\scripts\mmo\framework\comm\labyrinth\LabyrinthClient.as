package mmo.framework.comm.labyrinth
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class LabyrinthClient
   {
      public static const CMD_GET_ACHIEVE_STATE:String = "9_3_1";
      
      public static const CMD_GET_ACHIEVE_POINT:String = "9_3_2";
      
      public static const CMD_GET_ACHIEVE_RANK:String = "9_3_3";
      
      public static const CMD_GET_TIME_RANK:String = "9_3_4";
      
      public static const CMD_NOTIFY_ACHIEVE:String = "9_3_5";
      
      public static const NOTIFY_FINISH:String = "9_3_6";
      
      public static const CMD_GET_MAZE_BONUS:String = "9_3_7";
      
      private static var _instance:LabyrinthClient = null;
      
      private const EXTENSION_NAME:String = ExtMap.PVEExtension;
      
      public function LabyrinthClient()
      {
         super();
      }
      
      public static function get instance() : LabyrinthClient
      {
         if(_instance == null)
         {
            _instance = new LabyrinthClient();
         }
         return _instance;
      }
      
      public function getAchieveState(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_ACHIEVE_STATE,{"id":param1});
      }
      
      public function getAchievePoint(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_ACHIEVE_POINT,{"id":param1});
      }
      
      public function getAchieveRank(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(CMD_GET_ACHIEVE_RANK,{
            "id":param1,
            "page":param2,
            "unit":param3
         });
      }
      
      public function getTimeRank(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(CMD_GET_TIME_RANK,{
            "id":param1,
            "page":param2,
            "unit":param3
         });
      }
      
      public function getMazeBonus(param1:int) : void
      {
         this.sendXtMessage(CMD_GET_MAZE_BONUS,{"id":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object = null) : void
      {
         SocketClient.instance.sendXtMessage(this.EXTENSION_NAME,param1,param2);
      }
   }
}

