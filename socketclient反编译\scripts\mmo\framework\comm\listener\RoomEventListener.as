package mmo.framework.comm.listener
{
   import flash.events.EventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class RoomEventListener
   {
      private var _source:EventDispatcher;
      
      private var _dispatcher:EventDispatcher;
      
      public function RoomEventListener(param1:EventDispatcher, param2:EventDispatcher)
      {
         super();
         this._source = param1;
         this._dispatcher = param2;
      }
      
      public function set enable(param1:<PERSON>olean) : void
      {
         if(param1)
         {
            this._source.addEventListener(SSEvent.onJoinRoom,this.onJoinRoom);
            this._source.addEventListener(SSEvent.onJoinRoomError,this.onJoinRoomError);
            this._source.addEventListener(SSEvent.onUserEnterRoom,this.onUserEnterRoom);
            this._source.addEventListener(SSEvent.onUserLeaveRoom,this.onUserLeaveRoom);
            this._source.addEventListener(SSEvent.onRoomVariablesUpdate,this.onRoomVariablesUpdate);
            this._source.addEventListener(SSEvent.onRoomLeft,this.onRoomLeft);
         }
         else
         {
            this._source.removeEventListener(SSEvent.onJoinRoom,this.onJoinRoom);
            this._source.removeEventListener(SSEvent.onJoinRoomError,this.onJoinRoomError);
            this._source.removeEventListener(SSEvent.onUserEnterRoom,this.onUserEnterRoom);
            this._source.removeEventListener(SSEvent.onUserLeaveRoom,this.onUserLeaveRoom);
            this._source.removeEventListener(SSEvent.onRoomVariablesUpdate,this.onRoomVariablesUpdate);
         }
      }
      
      private function onJoinRoom(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onJoinRoom,param1.params);
         this._dispatcher.dispatchEvent(_loc2_);
      }
      
      private function onJoinRoomError(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onJoinRoomError,param1.params);
         this._dispatcher.dispatchEvent(_loc2_);
      }
      
      private function onUserEnterRoom(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserEnterRoom,param1.params);
         this._dispatcher.dispatchEvent(_loc2_);
      }
      
      private function onUserLeaveRoom(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserLeaveRoom,param1.params);
         this._dispatcher.dispatchEvent(_loc2_);
      }
      
      private function onRoomVariablesUpdate(param1:SSEvent) : void
      {
         var _loc2_:Array = param1.params.changedVars;
         this._dispatcher.dispatchEvent(new SocketClientEvent(SocketClientEvent.onRoomVariablesUpdate,param1.params));
      }
      
      private function onRoomLeft(param1:SSEvent) : void
      {
         this._dispatcher.dispatchEvent(new SocketClientEvent(SocketClientEvent.onUserLeaveRoom,param1.params));
      }
   }
}

