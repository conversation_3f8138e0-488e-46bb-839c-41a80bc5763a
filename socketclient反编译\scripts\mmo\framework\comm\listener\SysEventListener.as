package mmo.framework.comm.listener
{
   import flash.events.EventDispatcher;
   import flash.utils.ByteArray;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.socketserver.SSEvent;
   
   public class SysEventListener
   {
      private var _source:EventDispatcher;
      
      private var _dispatcher:EventDispatcher;
      
      private var _cmdHandlers:Object;
      
      public function SysEventListener(param1:EventDispatcher, param2:EventDispatcher, param3:Object)
      {
         super();
         this._source = param1;
         this._dispatcher = param2;
         this._cmdHandlers = param3;
      }
      
      public function set enable(param1:Boolean) : void
      {
         if(param1)
         {
            this._source.addEventListener(SSEvent.onConnectionLost,this.onConnectionLost);
            this._source.addEventListener(SSEvent.onAdminMessage,this.onAdminMessage);
            this._source.addEventListener(SSEvent.onPublicMessage,this.onPublicMessage);
            this._source.addEventListener(SSEvent.onObjectReceived,this.onObjectReceived);
            this._source.addEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
            this._source.addEventListener(SSEvent.onExtensionResponseByte,this.onExtensionResponseByte);
         }
         else
         {
            this._source.removeEventListener(SSEvent.onConnectionLost,this.onConnectionLost);
            this._source.removeEventListener(SSEvent.onAdminMessage,this.onAdminMessage);
            this._source.removeEventListener(SSEvent.onPublicMessage,this.onPublicMessage);
            this._source.removeEventListener(SSEvent.onObjectReceived,this.onObjectReceived);
            this._source.removeEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
            this._source.removeEventListener(SSEvent.onExtensionResponseByte,this.onExtensionResponseByte);
         }
      }
      
      private function onConnectionLost(param1:SSEvent) : void
      {
         var event:SocketClientEvent = null;
         var evt:SSEvent = param1;
         try
         {
            event = new SocketClientEvent(SocketClientEvent.onConnectionLost,evt.params);
            this._dispatcher.dispatchEvent(event);
         }
         catch(err:VerifyError)
         {
            if(err.errorID != 1024)
            {
               throw new Error("啊啊啊啊" + err.errorID);
            }
         }
      }
      
      private function onAdminMessage(param1:SSEvent) : void
      {
         var _loc2_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onAdminMessage,param1.params);
         this._dispatcher.dispatchEvent(_loc2_);
      }
      
      private function onPublicMessage(param1:SSEvent) : void
      {
         var _loc2_:String = param1.params.message;
         var _loc3_:int = int(_loc2_.indexOf("]"));
         var _loc4_:String = _loc2_.substring(0,_loc3_ + 1);
         var _loc5_:String = _loc2_.substr(_loc3_ + 1);
         var _loc6_:Function = this._cmdHandlers[_loc4_];
         _loc6_.apply(this,[param1,_loc5_]);
      }
      
      private function onObjectReceived(param1:SSEvent) : void
      {
         var _loc2_:* = param1.params.obj.cmd;
         var _loc3_:Function = this._cmdHandlers[_loc2_];
         _loc3_.apply(this,[param1]);
      }
      
      private function onExtensionResponse(param1:SSEvent) : void
      {
         var _loc5_:SocketClientEvent = null;
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:String = _loc2_._cmd;
         var _loc4_:Function = this._cmdHandlers[_loc3_];
         if(_loc4_ != null)
         {
            _loc4_.apply(this,[param1]);
         }
         else
         {
            _loc5_ = new SocketClientEvent(_loc3_,_loc2_);
            this._dispatcher.dispatchEvent(_loc5_);
         }
      }
      
      private function onExtensionResponseByte(param1:SSEvent) : void
      {
         var _loc5_:SocketClientEvent = null;
         var _loc2_:ByteArray = param1.params.dataObj;
         var _loc3_:int = _loc2_.readByte();
         var _loc4_:Function = this._cmdHandlers[_loc3_];
         if(_loc4_ != null)
         {
            _loc4_.apply(this,[param1]);
         }
         else
         {
            _loc5_ = new SocketClientEvent(_loc3_.toString(),_loc2_);
            this._dispatcher.dispatchEvent(_loc5_);
         }
      }
   }
}

