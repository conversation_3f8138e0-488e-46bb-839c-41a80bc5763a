package mmo.framework.comm.listener
{
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import mmo.framework.comm.SocketClientEvent;
   import mmo.framework.comm.UserVarKeys;
   import mmo.framework.comm.data.PositionChangeType;
   import mmo.framework.comm.data.UserPoint;
   import mmo.interfaces.camp.event.CampEvent;
   import mmo.interfaces.camp.event.CampEventDispatcher;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.data.User;
   
   public class UserVarListener
   {
      private var _source:EventDispatcher;
      
      private var _dispatcher:IEventDispatcher;
      
      public function UserVarListener(param1:EventDispatcher, param2:IEventDispatcher)
      {
         super();
         this._source = param1;
         this._dispatcher = param2;
      }
      
      public function set enable(param1:Boolean) : *
      {
         if(param1)
         {
            this._source.addEventListener(SSEvent.onUserVariablesUpdate,this.onUserVariablesUpdate);
         }
         else
         {
            this._source.removeEventListener(SSEvent.onUserVariablesUpdate,this.onUserVariablesUpdate);
         }
      }
      
      private function onUserVariablesUpdate(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params.changedVars;
         if(Boolean(_loc2_.pt))
         {
            this.handleUserPosition(param1);
         }
         else if(Boolean(_loc2_.dir))
         {
            this.handleChangeDirection(param1);
         }
         else if(Boolean(_loc2_.avatar))
         {
            this.handleAvatarChange(param1);
         }
         else if(Boolean(_loc2_.equip))
         {
            this.handleEquipChange(param1);
         }
         else if(Boolean(_loc2_.gameRoom) || Boolean(_loc2_.gameIndex))
         {
            this.handleGameRoomChange(param1);
         }
         else if(Boolean(_loc2_.float))
         {
            this.handleFloatStateChange(param1);
         }
         else if(Boolean(_loc2_.ride))
         {
            this.handleRideStateChange(param1);
         }
         else if(Boolean(_loc2_.t))
         {
            this.handleStateChange(param1);
         }
         else if(Boolean(_loc2_.av))
         {
            this.handleAdditionalChange(param1);
         }
         else if(Boolean(_loc2_.mi))
         {
            this.handleMemberStateChange(param1);
         }
         else if(Boolean(_loc2_.skills))
         {
            this.handleSkillListChange(param1);
         }
         else if(Boolean(_loc2_.family))
         {
            this.handleFamilyStateChange(param1);
         }
         else if(Boolean(_loc2_.army))
         {
            this.handleArmyStateChange(param1);
         }
         else if(Boolean(_loc2_.soul))
         {
            this.handleEquipSoulChange(param1);
         }
         else if(Boolean(_loc2_.fly_wing))
         {
            this.handleFlywingStateChange(param1);
         }
         else if(Boolean(_loc2_.campvar))
         {
            this.handleCampVarStateChange(param1);
         }
         else if(Boolean(_loc2_.gold_carriage))
         {
            this.handleGoldCarriageStateChange(param1);
         }
      }
      
      private function handleGoldCarriageStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.goldCarriage = String(_loc2_.getVariable("gold_carriage"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserGoldCarriageStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleCampVarStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.campvar = String(_loc2_.getVariable("campvar"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserCampStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
         CampEventDispatcher.instance.dispatchEvent(new CampEvent(CampEvent.NOTIFY_PERSON_UPATE,{
            "camp":_loc3_.campvar,
            "user":_loc2_
         }));
      }
      
      private function handleFlywingStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.flywingId = int(_loc2_.getVariable("fly_wing"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserFlywingStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleUserPosition(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = param1.params;
         var _loc4_:UserPoint = UserPoint.getUserPoint(_loc2_);
         _loc3_.point = _loc4_;
         var _loc5_:SocketClientEvent = null;
         switch(_loc4_.pointType)
         {
            case PositionChangeType.InitPoint:
               _loc5_ = new SocketClientEvent(SocketClientEvent.onInitUserPosition,_loc3_);
               break;
            case PositionChangeType.MovePoint:
               _loc5_ = new SocketClientEvent(SocketClientEvent.onUserMove,_loc3_);
               break;
            case PositionChangeType.JumpPoint:
               _loc5_ = new SocketClientEvent(SocketClientEvent.onUserJump,_loc3_);
         }
         this._dispatcher.dispatchEvent(_loc5_);
      }
      
      public function handleChangeDirection(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params;
         var _loc3_:User = param1.params.user;
         _loc2_.dir = _loc3_.getVariable("dir");
         this._dispatcher.dispatchEvent(new SocketClientEvent(SocketClientEvent.onUserChangeDirection,_loc2_));
      }
      
      private function handleAvatarChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = new Object();
         _loc3_.user = _loc2_;
         _loc3_.avatar = _loc2_.getVariable("avatar");
         _loc3_.cv = _loc2_.getVariable(UserVarKeys.VipClothesView);
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserAvatarChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleEquipChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = new Object();
         _loc3_.user = _loc2_;
         _loc3_.equip = _loc2_.getVariable("equip");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserEquipChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleGameRoomChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = param1.params;
         _loc3_.gameRoom = _loc2_.getVariable("gameRoom");
         _loc3_.gameNum = _loc2_.getVariable("gameNum");
         _loc3_.gameIndex = _loc2_.getVariable("gameIndex");
         _loc3_.isTempRoom = _loc2_.getVariable("isTempRoom");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onSetUserGameRoom,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleFloatStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = new Object();
         _loc3_.user = _loc2_;
         _loc3_.float = _loc2_.getVariable("float");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserFloatStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleRideStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.petId = int(_loc2_.getVariable("ride"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserRideStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = new Object();
         _loc3_.user = _loc2_;
         _loc3_.t = _loc2_.getVariable("t");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleAdditionalChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = new Object();
         _loc3_.user = _loc2_;
         _loc3_.additional = _loc2_.getVariable("av");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserAdditionalClothesChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleMemberStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.memberInfo = String(_loc2_.getVariable("mi"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserMemberStateChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleSkillListChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.user = _loc2_;
         _loc3_.skills = _loc2_.getVariable("skills");
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserSkillListChange,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleFamilyStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.familyInfo = String(_loc2_.getVariable("family"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onFamilySimpleInfoUpdate,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleArmyStateChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.armyInfo = String(_loc2_.getVariable("army"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserArmyInfoUpdate,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
      
      private function handleEquipSoulChange(param1:SSEvent) : void
      {
         var _loc2_:User = param1.params.user;
         var _loc3_:Object = {};
         _loc3_.soul = String(_loc2_.getVariable("soul"));
         _loc3_.user = _loc2_;
         var _loc4_:SocketClientEvent = new SocketClientEvent(SocketClientEvent.onUserEquipSoulUpdate,_loc3_);
         this._dispatcher.dispatchEvent(_loc4_);
      }
   }
}

