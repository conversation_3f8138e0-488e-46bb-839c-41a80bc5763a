package mmo.framework.comm.login
{
   import flash.events.EventDispatcher;
   import mmo.SSLEvent;
   import mmo.ServerInfo;
   import mmo.loader.error2yunwei.CollectErrorToYunWeiEvent;
   import mmo.loader.error2yunwei.CollectErrorToYunWeiManager;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   
   public class SocketLoginProxy extends EventDispatcher
   {
      private var client:SocketServerClient;
      
      private var curServerInfo:ServerInfo;
      
      private var loadingServerInfo:ServerInfo;
      
      private var userName:String;
      
      private var password:String;
      
      public function SocketLoginProxy(param1:SocketServerClient)
      {
         super();
         this.client = param1;
      }
      
      public function login(param1:String, param2:int, param3:String, param4:String, param5:String, param6:Boolean = false) : void
      {
         this.loadingServerInfo = new ServerInfo();
         this.loadingServerInfo.serverIP = param1;
         this.loadingServerInfo.port = param2;
         this.loadingServerInfo.zone = param3;
         this.userName = param4;
         this.password = param5;
         this.setListener(true);
         if(!this.client.isConnected)
         {
            this.client.connect(this.loadingServerInfo.serverIP,this.loadingServerInfo.port);
         }
         else if(this.client.myUserName == "")
         {
            this.doLogin();
         }
      }
      
      public function dispose() : void
      {
         this.setListener(false);
      }
      
      private function setListener(param1:Boolean) : void
      {
         if(param1)
         {
            this.client.addEventListener(SSEvent.onConnection,this.onConnection);
            this.client.addEventListener(SSEvent.onLogin,this.onLogin);
            this.client.addEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
         }
         else
         {
            this.client.removeEventListener(SSEvent.onConnection,this.onConnection);
            this.client.removeEventListener(SSEvent.onLogin,this.onLogin);
            this.client.removeEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
         }
      }
      
      private function doLogin() : void
      {
         this.client.login(this.loadingServerInfo.zone,this.userName,this.password);
      }
      
      private function onConnection(param1:SSEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:SSLEvent = null;
         if(!param1.params.success)
         {
            CollectErrorToYunWeiManager.sendSocketOrBattleError(CollectErrorToYunWeiEvent.SocketErorConnect,this.loadingServerInfo.serverIP,this.loadingServerInfo.port.toString(),this.loadingServerInfo.zone);
            this.setListener(false);
            _loc2_ = param1.params;
            _loc2_.success = false;
            _loc2_.error = "网络异常，请更换浮空岛或重新登录！若仍有问题，请致电客服020—83995251";
            _loc3_ = new SSLEvent(SSLEvent.onLogin,_loc2_);
            this.dispatchEvent(_loc3_);
         }
         else
         {
            this.doLogin();
         }
      }
      
      private function onLogin(param1:SSEvent) : void
      {
         if(param1.params.success == true)
         {
            this.curServerInfo = this.loadingServerInfo;
         }
         else
         {
            CollectErrorToYunWeiManager.sendSocketOrBattleError(CollectErrorToYunWeiEvent.SocketErorLogin,this.loadingServerInfo.serverIP,this.loadingServerInfo.port.toString(),this.loadingServerInfo.zone);
            this.client.disconnect();
         }
         var _loc2_:SSLEvent = new SSLEvent(SSLEvent.onLogin,param1.params);
         this.dispatchEvent(_loc2_);
      }
      
      private function onExtensionResponse(param1:SSEvent) : void
      {
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:String = _loc2_._cmd;
         var _loc4_:Object = {};
         switch(_loc3_)
         {
            case "logOK":
               this.client.myUserId = _loc2_.id;
               this.client.myUserName = _loc2_.n;
               _loc4_.success = true;
               _loc4_.name = _loc2_.n;
               _loc4_.error = "";
               this.curServerInfo = this.loadingServerInfo;
               break;
            case "logKO":
               CollectErrorToYunWeiManager.sendSocketOrBattleError(CollectErrorToYunWeiEvent.SocketErorZone,this.loadingServerInfo.serverIP,this.loadingServerInfo.port.toString(),this.loadingServerInfo.zone);
               _loc4_.success = false;
               _loc4_.error = _loc2_.err;
               this.client.disconnect();
               break;
            default:
               return;
         }
         this.setListener(false);
         var _loc5_:SSLEvent = new SSLEvent(SSLEvent.onLogin,_loc4_);
         this.dispatchEvent(_loc5_);
      }
   }
}

