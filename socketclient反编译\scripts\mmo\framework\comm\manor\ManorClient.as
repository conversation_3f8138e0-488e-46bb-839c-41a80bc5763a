package mmo.framework.comm.manor
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class ManorClient
   {
      public static const CMD_LOAD_MANOR_ROOM:String = "105_1";
      
      public static const CMD_LOAD_MANOR:String = "105_2";
      
      public static const CMD_DIG_UP_LAND:String = "105_201";
      
      public static const CMD_PLANT_CROP:String = "105_202";
      
      public static const CMD_REMOVE_CROP:String = "105_203";
      
      public static const CMD_APPLY_MANURE:String = "105_204";
      
      public static const CMD_REMOVE_DISASTER:String = "105_205";
      
      public static const CMD_GATHER_CROP:String = "105_206";
      
      public static const CMD_STEAL_CROP:String = "105_207";
      
      public static const CMD_PUT_FLY_PET:String = "105_300";
      
      public static const CMD_REMOVE_FLY_PET:String = "105_301";
      
      public static const CMD_ADD_FEED:String = "105_302";
      
      public static const CMD_GAIN_FEED_AREA_INCOME:String = "105_303";
      
      public static const CMD_GET_ROBOT_VISITOR:String = "105_304";
      
      public static const CMD_GET_FLYING_PET_EGG:String = "105_305";
      
      public static const CMD_LOAD_MANOR_BUDDY_LIST:String = "105_401";
      
      public static const CMD_GET_STORAGE_DATA:String = "105_3";
      
      public static const NOTIFY_UPDATE_STORAGE_DATA:String = "105_4";
      
      public static const NOTIFY_MANOR_EVENT:String = "105_5";
      
      public static const CMD_BUY_SPIRIT:String = "105_6";
      
      public static const CMD_SELL:String = "105_7";
      
      public static const CMD_GET_OTHER_INFO:String = "105_8";
      
      public static const CMD_BUY:String = "105_9";
      
      public static const CMD_LOAD_MANOR_EVENT:String = "105_402";
      
      public static const CMD_LOAD_MY_DECORATE_STORAGE:String = "105_501";
      
      public static const CMD_ADD_FITMENT_TO_SCENE:String = "105_502";
      
      public static const CMD_TAKE_BACK_FITMENT_FROM_SCENE:String = "105_503";
      
      public static const CMD_GET_FIRST_BONUS:String = "105_10";
      
      public static const CMD_COMBINE_FITMENT:String = "105_504";
      
      public static const CMD_BUY_DESIGN_DRAWING_USE_COIN:String = "105_505";
      
      public static const CMD_SET_MANOR_SCENE_FITMENT_SEQ:String = "105_506";
      
      public static const CMD_SUBMIT_ORDER:String = "105_11";
      
      public static const CMD_OPEN_BOX:String = "105_12";
      
      public static const CMD_CLEAR_CREEP:String = "105_14";
      
      public static const CMD_LOAD_GUARDIAN_BONUS_LIST:String = "105_210";
      
      public static const CMD_GET_GUARDIAN_BONUS:String = "105_211";
      
      public static const CMD_SET_FEED_AREA_FLY_PET_SEQ:String = "105_306";
      
      public static const CMD_GET_FIT_FRIEND:String = "105_403";
      
      public static const CMD_GET_ORDERPET_BONUS:String = "105_16";
      
      public static const CMD_CHANGE_ORDER:String = "105_15";
      
      private static const EXTENSION_NAME:String = ExtMap.ManorExtension;
      
      public function ManorClient()
      {
         super();
      }
      
      public static function sendXtMsgWithCallBack(param1:String, param2:Function = null, param3:Object = null) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(EXTENSION_NAME,param1,param3,param2);
      }
   }
}

