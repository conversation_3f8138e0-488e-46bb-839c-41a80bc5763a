package mmo.framework.comm.map
{
   import mmo.framework.comm.SocketClient;
   
   public class MapClient
   {
      private static var _instance:MapClient;
      
      public static const EXTENSIONS_NAME:String = "MapExtension";
      
      public static const CMD_VIEW_MAP:String = "13_0";
      
      public static const CMD_UPDATE_MAP:String = "13_1";
      
      public static const REFRESH_CREEP_MANUAL:String = "13_2";
      
      public static const CREATE_ROOM:String = "13_3";
      
      public function MapClient()
      {
         super();
      }
      
      public static function get instance() : MapClient
      {
         if(_instance == null)
         {
            _instance = new MapClient();
         }
         return _instance;
      }
      
      public function createRoom(param1:String) : *
      {
         this.sendXtMessage(CREATE_ROOM,{"bn":param1});
      }
      
      public function viewMapConfig() : void
      {
         this.sendXtMessage(CMD_VIEW_MAP,{});
      }
      
      public function refreshMonster(param1:int) : void
      {
         this.sendXtMessage(REFRESH_CREEP_MANUAL,{"crId":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSIONS_NAME,param1,param2);
      }
   }
}

