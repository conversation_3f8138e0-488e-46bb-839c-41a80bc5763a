package mmo.framework.comm.material
{
   import mmo.framework.comm.SocketManager;
   
   public class ClothesClient
   {
      private static var _instance:ClothesClient;
      
      private static const EXTENSION_NAME:String = "MaterialExtension";
      
      public static const GET_USER_ALL_CLOTHES:String = "12_3_1";
      
      public static const GET_USER_WEARING_CLOTHES:String = "12_3_2";
      
      public static const UPDATE_MY_WEARING_CLOTHES:String = "12_3_3";
      
      public static const UPDATE_MY_ADDITIONAL_CLOTHES:String = "12_3_4";
      
      public static const CMD_CHANGE_MY_CLOTHES_NAME:String = "12_3_5";
      
      public static const CMD_GET_USER_CLOTHES_INFO:String = "12_3_6";
      
      public function ClothesClient()
      {
         super();
      }
      
      public static function get instance() : ClothesClient
      {
         if(!_instance)
         {
            _instance = new ClothesClient();
         }
         return _instance;
      }
      
      public function getUserClothes(param1:int) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.cid = param1;
         this.sendXtMessage(GET_USER_ALL_CLOTHES,_loc2_);
      }
      
      public function getUserWearingClothes(param1:int) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.cid = param1;
         this.sendXtMessage(GET_USER_WEARING_CLOTHES,_loc2_);
      }
      
      public function updateMyWearingClothes(param1:Array) : void
      {
         var _loc2_:Object = new Object();
         _loc2_.vpack = param1.join(",");
         this.sendXtMessage(UPDATE_MY_WEARING_CLOTHES,_loc2_);
      }
      
      public function updateMyAdditionalClothes(param1:Array) : void
      {
         var _loc2_:String = param1.join(",");
         var _loc3_:Object = {
            "op":"u",
            "list":_loc2_
         };
         this.sendXtMessage(UPDATE_MY_ADDITIONAL_CLOTHES,_loc3_);
      }
      
      public function clearMyAdditionalClothes() : void
      {
         var _loc1_:Object = {"op":"c"};
         this.sendXtMessage(UPDATE_MY_ADDITIONAL_CLOTHES,_loc1_);
      }
      
      public function changeMyClothesName(param1:int, param2:String, param3:int) : void
      {
         var _loc4_:Object = {
            "type":param1,
            "content":param2,
            "mid":param3
         };
         this.sendXtMessage(CMD_CHANGE_MY_CLOTHES_NAME,_loc4_);
      }
      
      public function getUserClothesInfo(param1:int) : void
      {
         var _loc2_:Object = {"cid":param1};
         this.sendXtMessage(CMD_GET_USER_CLOTHES_INFO,_loc2_);
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

