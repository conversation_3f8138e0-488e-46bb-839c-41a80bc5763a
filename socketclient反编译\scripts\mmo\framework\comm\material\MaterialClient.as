package mmo.framework.comm.material
{
   import mmo.framework.comm.SocketClient;
   import mmo.framework.comm.SocketManager;
   
   public class MaterialClient
   {
      private static var _instance:MaterialClient;
      
      private static const EXTENSIONS_NAME:String = "MaterialExtension";
      
      public static const cmdBuyMaterial:String = "8_2_0";
      
      public static const cmdSaleMaterial:String = "8_2_1";
      
      public static const cmdGetFixupPrice:String = "8_2_2";
      
      public static const cmdFixupEquipment:String = "8_2_3";
      
      public static const cmdTransport:String = "8_2_4";
      
      public static const cmdStrengthenCondition:String = "8_2_5";
      
      public static const cmdStrengthen:String = "8_2_6";
      
      public static const cmdExchange:String = "8_2_7";
      
      public static const cmdComposeStone:String = "8_2_8";
      
      public static const cmdDecomposeStone:String = "8_2_9";
      
      public static const cmdGetWeeklyScoreAward:String = "8_2_10";
      
      public static const cmdGetMyBackpack:String = "8_0_0";
      
      public static const cmdDressEquipment:String = "8_0_1";
      
      public static const cmdUndressEquipment:String = "8_0_2";
      
      public static const cmdUseConsument:String = "8_0_3";
      
      public static const cmdAddMaterial:String = "8_4_0";
      
      public static const cmdDecMaterial:String = "8_0_4";
      
      public static const cmdDecMaterialByMid:String = "8_0_5";
      
      public static const cmdRookieDragonPower:String = "8_10";
      
      public static const cmdGetBuyBackBuff:String = "8_2_13";
      
      public static const cmdBuyBackFromNpc:String = "8_2_14";
      
      public static const cmdGetExchangeRemainTimes:String = "8_2_16";
      
      public static const cmdGetDetailExchangeRemainTimes:String = "8_2_25";
      
      public static const cmdGetEquipStrProbability:String = "8_2_26";
      
      public static const cmdEquipStrWithBuLuoLiPower:String = "8_2_41";
      
      public static const cmdEquipStrToTwelve:String = "8_2_18";
      
      public static const onUpdateMaterial:String = "8_0";
      
      public static const cmdGetExchangeRemainTimesBatch:String = "8_2_27";
      
      public static const cmdBatchExchange:String = "8_2_30";
      
      public static const cmdGetFreeStrengthenTimes:String = "8_2_31";
      
      public static const cmdBatchGetExchangeDetailRemainTimes:* = "8_2_35";
      
      public function MaterialClient()
      {
         super();
      }
      
      public static function getInstance() : MaterialClient
      {
         if(_instance == null)
         {
            _instance = new MaterialClient();
         }
         return _instance;
      }
      
      public function buyMaterial(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(cmdBuyMaterial,{
            "t":param1,
            "id":param2,
            "n":param3
         });
      }
      
      public function getEquipStrProbability(param1:int, param2:int, param3:Boolean = false) : void
      {
         this.sendXtMessage(cmdGetEquipStrProbability,{
            "gid":param1,
            "li":param2,
            "useS":param3
         });
      }
      
      public function saleMaterial(param1:int, param2:int, param3:int, param4:int) : void
      {
         this.saleMaterials(param1 + ":" + param3 + ":" + param4);
      }
      
      public function saleMaterials(param1:String) : void
      {
         this.sendXtMessage(cmdSaleMaterial,{"sell":param1});
      }
      
      public function buyBackFromNpc(param1:int, param2:int, param3:int, param4:int) : void
      {
         this.sendXtMessage(cmdBuyBackFromNpc,{
            "t":param1,
            "mid":param2,
            "gi":param3,
            "n":param4
         });
      }
      
      public function getBuyBackData() : void
      {
         this.sendXtMessage(cmdGetBuyBackBuff,{});
      }
      
      public function getMyBackpack() : void
      {
         this.sendXtMessage(cmdGetMyBackpack,{});
      }
      
      public function dressEquipment(param1:uint) : void
      {
         this.sendXtMessage(cmdDressEquipment,{"gid":param1});
      }
      
      public function undressEquipment(param1:uint) : void
      {
         this.sendXtMessage(cmdUndressEquipment,{"gid":param1});
      }
      
      public function fixupEquipment(param1:Array, param2:int) : void
      {
         this.sendXtMessage(cmdFixupEquipment,{
            "gid":param1,
            "type":param2
         });
      }
      
      public function transportToDes(param1:uint) : void
      {
         this.sendXtMessage(cmdTransport,{"money":param1});
      }
      
      public function transportByWings(param1:String) : void
      {
         this.sendXtMessage(cmdTransport,{
            "type":1,
            "des":param1
         });
      }
      
      public function strengthenCondition(param1:uint) : void
      {
         this.sendXtMessage(cmdStrengthenCondition,{"gid":param1});
      }
      
      public function strengthen(param1:uint, param2:int, param3:int) : void
      {
         this.sendXtMessage(cmdStrengthen,{
            "gi":param1,
            "pi":param2,
            "li":param3
         });
      }
      
      public function strengthenToTwelve(param1:uint, param2:int, param3:Boolean) : void
      {
         this.sendXtMessage(cmdEquipStrToTwelve,{
            "gid":param1,
            "mid":param2,
            "ob":param3
         });
      }
      
      public function strengthenBuluoli(param1:uint) : void
      {
         this.sendXtMessage(cmdEquipStrWithBuLuoLiPower,{"gid":param1});
      }
      
      public function composeStone(param1:uint, param2:uint) : void
      {
         this.sendXtMessage(cmdComposeStone,{
            "fid":param1,
            "n":param2
         });
      }
      
      public function decomposeStone(param1:Array) : void
      {
         this.sendXtMessage(cmdDecomposeStone,{"gid":param1});
      }
      
      public function rookieDragonPower() : void
      {
         this.sendXtMessage(cmdRookieDragonPower,{});
      }
      
      public function getFixupPrice(param1:Array, param2:int) : void
      {
         this.sendXtMessage(cmdGetFixupPrice,{
            "gid":param1,
            "type":param2
         });
      }
      
      public function addMaterial(param1:int, param2:uint, param3:int) : void
      {
         this.sendXtMessage(cmdAddMaterial,{
            "t":param1,
            "m":param2,
            "n":param3
         });
      }
      
      public function decMaterial(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(cmdDecMaterial,{
            "type":param1,
            "gid":param2,
            "n":param3
         });
      }
      
      public function decMaterialByMid(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(cmdDecMaterialByMid,{
            "type":param1,
            "mid":param2,
            "n":param3
         });
      }
      
      public function useConsument(param1:uint, param2:uint) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSIONS_NAME,cmdUseConsument,{
            "gid":param1,
            "n":param2
         });
      }
      
      public function exchange(param1:int, param2:int) : void
      {
         this.sendXtMessage(cmdExchange,{
            "fid":param1,
            "n":param2
         });
      }
      
      public function getWeeklyAward(param1:int) : void
      {
         this.sendXtMessage(cmdGetWeeklyScoreAward,{"sc":param1});
      }
      
      public function getExchangeRemainTimes(param1:int) : void
      {
         this.sendXtMessage(cmdGetExchangeRemainTimes,{"fid":param1});
      }
      
      public function getDetailExchangeRemainTimes(param1:int) : void
      {
         this.sendXtMessage(cmdGetDetailExchangeRemainTimes,{"fid":param1});
      }
      
      public function exchangeBatch(param1:String) : void
      {
         this.sendXtMessage(cmdBatchExchange,{"f":param1});
      }
      
      public function getExchangeRemainTimesBatch(param1:Array) : void
      {
         this.sendXtMessage(cmdGetExchangeRemainTimesBatch,{"fid":param1});
      }
      
      public function getExchangeRemainTimeDetailBatch(param1:String) : void
      {
         this.sendXtMessage(cmdBatchGetExchangeDetailRemainTimes,{"ids":param1});
      }
      
      public function getFreeStrengthenTimes() : void
      {
         this.sendXtMessage(cmdGetFreeStrengthenTimes,null);
      }
      
      final protected function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(EXTENSIONS_NAME,param1,param2);
      }
   }
}

