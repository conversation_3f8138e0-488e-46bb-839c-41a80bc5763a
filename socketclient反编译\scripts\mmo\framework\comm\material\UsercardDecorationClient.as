package mmo.framework.comm.material
{
   import mmo.framework.comm.SocketManager;
   
   public class UsercardDecorationClient
   {
      private static var _instance:UsercardDecorationClient;
      
      private static const EXTENSION_NAME:String = "MaterialExtension";
      
      public static const cmdGetUserAllDecoration:String = "29_1";
      
      public static const cmdGetUserUsingDecorations:String = "29_2";
      
      public static const cmdUpdateUsingDecorations:String = "29_3";
      
      public function UsercardDecorationClient()
      {
         super();
      }
      
      public static function get instance() : UsercardDecorationClient
      {
         if(!_instance)
         {
            _instance = new UsercardDecorationClient();
         }
         return _instance;
      }
      
      public function getMyAllDecorations() : *
      {
         this.sendXtMessage(cmdGetUserAllDecoration,null);
      }
      
      public function getUserUsingDecorations(param1:int) : *
      {
         this.sendXtMessage(cmdGetUserUsingDecorations,{"u":param1});
      }
      
      public function updateMyUsingDecorations(param1:Array) : *
      {
         this.sendXtMessage(cmdUpdateUsingDecorations,{"c":param1.join(",")});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

