package mmo.framework.comm.movement
{
   import flash.events.EventDispatcher;
   import flash.utils.ByteArray;
   import mmo.framework.comm.SocketManager;
   
   public class MovementClient extends EventDispatcher
   {
      private static var _instance:MovementClient;
      
      private static const EXTENSIONS_NAME:String = "PJKExtension";
      
      private static const RQST_CHARACTER_VECTOR_UPDATE:* = "1401";
      
      private static const RQST_CHARACTER_JUMP_UPDATE:* = "1402";
      
      public function MovementClient()
      {
         super();
      }
      
      public static function getInstance() : MovementClient
      {
         if(_instance == null)
         {
            _instance = new MovementClient();
         }
         return _instance;
      }
      
      public function updateMoveVector(param1:int, param2:int, param3:uint, param4:int, param5:uint, param6:int) : void
      {
         var _loc7_:ByteArray = new ByteArray();
         _loc7_.writeShort(param1);
         _loc7_.writeShort(param2);
         _loc7_.writeByte((param3 << 4) + param5);
         _loc7_.writeByte(param4);
         _loc7_.writeInt(param6);
         SocketManager.instance.activeSocketClient.sendXtMessageByte(1,_loc7_);
      }
      
      public function updateJumpHeight(param1:uint, param2:int) : void
      {
         this.sendXtMessage(RQST_CHARACTER_JUMP_UPDATE,{
            "h":param1,
            "bi":param2
         });
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketManager.instance.activeSocketClient.sendXtMessage(EXTENSIONS_NAME,param1,param2);
      }
   }
}

