package mmo.framework.comm.pve
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class PVEClient
   {
      public static const REQUEST_JOIN_PVE:String = "9_5_0";
      
      public static const REFRESH_MONSTERS:String = "9_5_1";
      
      public static const PASS_LAYER:String = "9_5_2";
      
      public static const MANUAL_EXIT:String = "9_5_3";
      
      public static const GET_USER_INFO:String = "9_5_4";
      
      public static const BATCH_GET_INFO:String = "9_5_5";
      
      public static const ADD_TODAY_TIMES:String = "9_5_6";
      
      public static const LONGBI_POWER:String = "9_5_7";
      
      public static const RELIVE:String = "9_5_8";
      
      public static const PERSIST_TIME_SUCCESS:String = "9_5_9";
      
      public static const REFRESH_NEXT:String = "9_5_10";
      
      private static const EXTENSION_NAME:String = ExtMap.PVEExtension;
      
      private static var _instance:PVEClient = null;
      
      public function PVEClient()
      {
         super();
      }
      
      public static function get instance() : PVEClient
      {
         if(_instance == null)
         {
            _instance = new PVEClient();
         }
         return _instance;
      }
      
      public function sendRequestJoinPVEScene(param1:uint, param2:uint) : void
      {
         this.sendXtMessage(REQUEST_JOIN_PVE,{
            "id":param1,
            "li":param2
         });
      }
      
      public function sendRefreshMonsters() : void
      {
         this.sendXtMessage(REFRESH_MONSTERS,{});
      }
      
      public function exitManual() : void
      {
         this.sendXtMessage(MANUAL_EXIT,{});
      }
      
      public function getUserInfo(param1:uint) : void
      {
         this.sendXtMessage(GET_USER_INFO,{"id":param1});
      }
      
      public function getUserInfos(param1:Array) : void
      {
         this.sendXtMessage(BATCH_GET_INFO,{"id":param1});
      }
      
      public function addTodayTime(param1:int) : void
      {
         this.sendXtMessage(ADD_TODAY_TIMES,{"id":param1});
      }
      
      public function longbiPass(param1:int, param2:int) : void
      {
         this.sendXtMessage(LONGBI_POWER,{
            "id":param1,
            "li":param2
         });
      }
      
      public function revive(param1:Boolean) : void
      {
         this.sendXtMessage(RELIVE,{"lb":param1});
      }
      
      public function persistTimeSuccess() : void
      {
         this.sendXtMessage(PERSIST_TIME_SUCCESS,{});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

