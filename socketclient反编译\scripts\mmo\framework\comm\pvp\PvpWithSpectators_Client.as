package mmo.framework.comm.pvp
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class PvpWithSpectators_Client
   {
      private static const pre:* = "61_";
      
      public static const CMD_MAKE_REQUEST:* = pre + 1;
      
      public static const CMD_CANCEL_REQUEST:* = pre + 2;
      
      public static const CMD_REPLY:* = pre + 3;
      
      public static const CMD_LOADING:* = pre + 4;
      
      public static const CMD_READY:* = pre + 5;
      
      public static const CMD_COUNTDOWN:* = pre + 6;
      
      public static const CMD_FINISH:* = pre + 7;
      
      public static const CMD_BONUS_NOTIFY:* = pre + 8;
      
      public static const CMD_JOIN_VIEW:* = pre + 9;
      
      public static const CMD_VIEW_INFO:* = pre + 10;
      
      public static const CMD_VIEW_SELF:* = pre + 11;
      
      public static const CMD_GET_TIME:* = pre + 12;
      
      public static const CMD_RETURN_ARENA:* = pre + 13;
      
      public function PvpWithSpectators_Client()
      {
         super();
      }
      
      public static function sendToBeHost() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_MAKE_REQUEST,{});
      }
      
      public static function sendChallage() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_REPLY,{});
      }
      
      public static function sendCancelBeingHost() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_CANCEL_REQUEST,{});
      }
      
      public static function sendToBeSpectator() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_JOIN_VIEW,{});
      }
      
      public static function sendGetPanelInfo() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_VIEW_INFO,{});
      }
      
      public static function sendGetSelfInfo() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_VIEW_SELF,{});
      }
      
      public static function sendReadyToFight() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_READY,{});
      }
      
      public static function sendSynTime() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_GET_TIME,{});
      }
      
      public static function sendHostReady() : void
      {
         SocketClient.instance.sendXtMessage(ExtMap.PvpWithSpectatorsExtension,CMD_RETURN_ARENA,{});
      }
   }
}

