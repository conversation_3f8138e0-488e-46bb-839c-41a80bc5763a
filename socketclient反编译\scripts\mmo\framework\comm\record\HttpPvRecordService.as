package mmo.framework.comm.record
{
   import com.adobe.utils.StringUtil;
   import com.hexagonstar.util.debug.Debug;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.events.TimerEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.utils.Timer;
   import mmo.common.DateUtil;
   import mmo.common.JsProxy;
   import mmo.common.shareobject.LocalStore;
   import mmo.common.shareobject.ShareObjectManager2;
   import mmo.common.user.UserInfo;
   import mmo.config.ConfigReader;
   
   public class HttpPvRecordService
   {
      private static var _Src:String;
      
      private static var _EventTime:String;
      
      private static var timeoutTimer:Timer;
      
      private static var recordNewUserEventCallBack:Function;
      
      public static const Machine_New:int = 0;
      
      public static const Machine_Old:int = 1;
      
      private static const KEY_MACHINE_STATE:String = "KEY_MACHINE_STATE_HTTP";
      
      private static const CREATE_COOKIE_TIME:String = "20170417_CREATE_COOKIE_TIME";
      
      private static const DAILY_LOCAL_KEY:String = "20170417_DAILY_LOCAL_KEY";
      
      private static const NEW_USER_EVENT_CMD:String = "newUser";
      
      private static var _IdintityId:String = "";
      
      private var pvRecordNewUserEventDefine:PvRecordNewUserEventDefine;
      
      private var vRecordTopicIdDefine:PvRecordTopicIdDefine;
      
      public function HttpPvRecordService()
      {
         super();
      }
      
      public static function get EventTime() : String
      {
         return _EventTime;
      }
      
      public static function get Src() : String
      {
         if(_Src == null)
         {
            _Src = getUrl();
         }
         return _Src;
      }
      
      public static function get IdintityId() : String
      {
         return _IdintityId;
      }
      
      public static function record() : void
      {
         var _loc1_:String = getUrl();
         if(_loc1_ == null)
         {
            return;
         }
         var _loc2_:int = isOldMachine() ? Machine_Old : Machine_New;
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.fromurl = _loc1_;
         _loc3_.machine = _loc2_;
         loadURLRequest(_loc3_);
      }
      
      public static function recordNewUserEvent(param1:String, param2:String = "", param3:Boolean = false, param4:Function = null) : void
      {
         var _loc8_:Date = null;
         var _loc9_:String = null;
         var _loc5_:String = getUrl();
         if(_loc5_ == null || _loc5_.length < 1 || _loc5_ == "empty")
         {
            if(param4 != null)
            {
               param4.call();
            }
            return;
         }
         if(param3)
         {
            if(!isDayFirstTimes(param1))
            {
               return;
            }
            _loc8_ = new Date();
            _loc9_ = DateUtil.formatDateYMD(_loc8_);
            LocalStore.setValue(createDailyKey(param1),_loc9_);
         }
         var _loc6_:int = isOldMachine() ? Machine_Old : Machine_New;
         var _loc7_:URLVariables = new URLVariables();
         _loc7_.cmd = NEW_USER_EVENT_CMD;
         _loc7_.topicId = PvRecordTopicIdDefine.PV_TOPIC_ID_10010;
         _loc7_.eventId = param1;
         _loc7_.fromurl = _loc5_;
         _loc7_.machine = _loc6_;
         _loc7_.duoduoId = param2 == "" ? UserInfo.userName : param2;
         if(param4 != null)
         {
            recordNewUserEventCallBack = param4;
            disposeTimeoutTimer();
            buildTimeoutTimer(_loc7_,onReceiveResponseAndCallBack);
         }
         else if(_IdintityId == null)
         {
            loadURLRequest(_loc7_,onReceiveResponse);
         }
         else
         {
            loadURLRequest(_loc7_);
         }
      }
      
      private static function onReceiveResponseAndCallBack(param1:*) : void
      {
         onReceiveResponse(param1);
         if(recordNewUserEventCallBack != null)
         {
            recordNewUserEventCallBack.call();
            recordNewUserEventCallBack = null;
         }
      }
      
      private static function disposeTimeoutTimer() : void
      {
         if(Boolean(timeoutTimer))
         {
            timeoutTimer.removeEventListener(TimerEvent.TIMER_COMPLETE,onTimeOut);
            timeoutTimer.stop();
            timeoutTimer = null;
         }
      }
      
      private static function buildTimeoutTimer(param1:URLVariables, param2:Function = null) : void
      {
         timeoutTimer = new Timer(5000,1);
         timeoutTimer.addEventListener(TimerEvent.TIMER_COMPLETE,onTimeOut);
         timeoutTimer.start();
         loadURLRequest(param1,param2);
      }
      
      private static function onTimeOut(param1:TimerEvent) : void
      {
         disposeTimeoutTimer();
         if(recordNewUserEventCallBack != null)
         {
            recordNewUserEventCallBack.call();
            recordNewUserEventCallBack = null;
         }
      }
      
      private static function onReceiveResponse(param1:*) : void
      {
         var _loc2_:XML = new XML(param1);
         var _loc3_:String = _loc2_.result;
         if(_loc3_ == "success")
         {
            _IdintityId = _loc2_.identityId || false;
            _Src = _loc2_.src || false;
            _EventTime = _loc2_.cct || false;
         }
      }
      
      public static function recordDataPlatformBaseProtocol(param1:String) : void
      {
         var _loc2_:URLVariables = new URLVariables();
         _loc2_.cmd = NEW_USER_EVENT_CMD;
         _loc2_.topicId = param1;
         _loc2_.duoduoId = UserInfo.userName;
         _loc2_.projectId = ConfigReader.instance.currentSiteType - 1;
         var _loc3_:String = JsProxy.getValue("getQS","","site");
         var _loc4_:String = JsProxy.getValue("getQS","","src");
         var _loc5_:String = _loc4_ == null || _loc4_ == "" ? JsProxy.getReferer() : _loc4_;
         var _loc6_:String = _loc3_ == null || _loc3_ == "" ? JsProxy.getFrom() : _loc3_;
         var _loc7_:String = JsProxy.getReferer();
         if(_loc7_ == null || _loc7_.length < 1)
         {
            return;
         }
         switch(param1)
         {
            case PvRecordTopicIdDefine.PV_TOPIC_ID_10001:
            case PvRecordTopicIdDefine.PV_TOPIC_ID_10002:
               _loc2_.from = _loc6_.indexOf("account.100bt.com") >= 0 ? _loc3_ : _loc6_;
               _loc2_.ref = _loc5_.indexOf("account.100bt.com") >= 0 ? _loc4_ : _loc5_;
               break;
            case PvRecordTopicIdDefine.PV_TOPIC_ID_10004:
               _loc2_.gender = !!UserInfo.gender ? "1" : "0";
         }
         loadURLRequest(_loc2_);
      }
      
      private static function loadURLRequest(param1:URLVariables, param2:Function = null) : void
      {
         var loader:URLLoader = null;
         var onSendSuc:Function = null;
         var variable:URLVariables = param1;
         var callBack:Function = param2;
         onSendSuc = function(param1:Event):void
         {
            loader.removeEventListener(IOErrorEvent.IO_ERROR,onIOError);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,onIOError);
            loader.removeEventListener(Event.COMPLETE,onSendSuc);
            callBack && callBack.apply(null,[loader.data]);
         };
         var site:String = ConfigReader.instance.serviceSite;
         var url:String = ConfigReader.instance.pvUrl;
         var request:URLRequest = new URLRequest(site + url);
         request.method = URLRequestMethod.POST;
         request.data = variable;
         loader = new URLLoader();
         loader.addEventListener(IOErrorEvent.IO_ERROR,onIOError);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,onIOError);
         if(callBack != null)
         {
            loader.addEventListener(Event.COMPLETE,onSendSuc);
         }
         loader.load(request);
      }
      
      private static function onIOError(param1:Event) : void
      {
         URLLoader(param1.target).removeEventListener(IOErrorEvent.IO_ERROR,onIOError);
         URLLoader(param1.target).removeEventListener(SecurityErrorEvent.SECURITY_ERROR,onIOError);
         URLLoader(param1.target).close();
      }
      
      private static function getUrl() : String
      {
         var _loc1_:String = getDomainUrl(JsProxy.getReferer());
         if(_loc1_ == null || StringUtil.trim(_loc1_) == "")
         {
            Debug.trace("url null");
            return "empty";
         }
         Debug.trace(_loc1_);
         return _loc1_;
      }
      
      private static function getDomainUrl(param1:String) : String
      {
         var _loc2_:* = param1.split("/");
         if(_loc2_.length == 1)
         {
            return param1;
         }
         return _loc2_[2];
      }
      
      private static function isNewUserMachine() : Boolean
      {
         var _loc2_:Date = null;
         var _loc3_:String = null;
         var _loc1_:Boolean = Boolean(LocalStore.getValue(CREATE_COOKIE_TIME));
         if(!_loc1_)
         {
            _loc2_ = new Date();
            _loc3_ = DateUtil.formatDateYMD(_loc2_);
            LocalStore.setValue(CREATE_COOKIE_TIME,_loc3_);
            return true;
         }
         return false;
      }
      
      private static function isDayFirstTimes(param1:String) : Boolean
      {
         var _loc2_:String = createDailyKey(param1);
         var _loc3_:Boolean = Boolean(LocalStore.getValue(_loc2_));
         var _loc4_:String = "";
         var _loc5_:Date = new Date();
         if(!_loc3_)
         {
            _loc4_ = DateUtil.formatDateYMD(_loc5_);
            LocalStore.setValue(_loc2_,_loc4_);
            return true;
         }
         _loc4_ = LocalStore.getValue(_loc2_) as String;
         if(_loc4_ == DateUtil.formatDateYMD(_loc5_))
         {
            return false;
         }
         return true;
      }
      
      private static function createDailyKey(param1:String) : String
      {
         return DAILY_LOCAL_KEY + "_" + param1;
      }
      
      private static function isOldMachine() : Boolean
      {
         var _loc1_:Vector.<Object> = null;
         _loc1_ = ShareObjectManager2.instance.getAllLocalUserInfo();
         var _loc2_:Boolean = _loc1_ != null && _loc1_.length > 0;
         var _loc3_:Boolean = Boolean(LocalStore.getValue(KEY_MACHINE_STATE));
         return _loc2_ || _loc3_;
      }
   }
}

