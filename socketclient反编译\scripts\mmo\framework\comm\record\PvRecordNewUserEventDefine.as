package mmo.framework.comm.record
{
   public class PvRecordNewUserEventDefine
   {
      public static const PV_NEW_USER_EVENT_1000:String = "1000";
      
      public static const PV_NEW_USER_EVENT_1001:String = "1001";
      
      public static const PV_NEW_USER_EVENT_1002:String = "1002";
      
      public static const PV_NEW_USER_EVENT_1003:String = "1003";
      
      public static const PV_NEW_USER_EVENT_1004:String = "1004";
      
      public static const PV_NEW_USER_EVENT_1005:String = "1005";
      
      public static const PV_NEW_USER_EVENT_1006:String = "1006";
      
      public static const PV_NEW_USER_EVENT_1007:String = "1007";
      
      public static const PV_NEW_USER_EVENT_1008:String = "1008";
      
      public static const PV_NEW_USER_EVENT_1009:String = "1009";
      
      public static const PV_NEW_USER_EVENT_1010:String = "1010";
      
      public static const PV_NEW_USER_EVENT_1011:String = "1011";
      
      public static const PV_NEW_USER_EVENT_1013:String = "1013";
      
      public static const PV_NEW_USER_EVENT_1014:String = "1014";
      
      public static const PV_NEW_USER_EVENT_1015:String = "1015";
      
      public static const PV_NEW_USER_EVENT_1016:String = "1016";
      
      public static const PV_NEW_USER_EVENT_1017:String = "1017";
      
      public function PvRecordNewUserEventDefine()
      {
         super();
      }
   }
}

