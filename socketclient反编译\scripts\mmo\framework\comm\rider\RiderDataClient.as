package mmo.framework.comm.rider
{
   import mmo.framework.comm.SocketClient;
   
   public class RiderDataClient
   {
      private static var _instance:RiderDataClient;
      
      private static const EXT_NAME:String = "RideExtension";
      
      public static const TRY_RIDE:String = "26_1";
      
      public static const TRY_UNRIDE:String = "26_2";
      
      public static const LOAD_ALL_RIDERS:String = "26_3";
      
      public static const LOAD_ALL_LICENSE:String = "26_4";
      
      public static const ON_LICENSE_UPDATE:String = "26_5";
      
      public static const ON_RIDE_PET_UPDATE:String = "26_6";
      
      public static const CHANGE_FIRST_RIDER:String = "26_7";
      
      public static const GetRidingPet:String = "26_8";
      
      public static const SetGuardianRide:String = "26_9";
      
      public static const CmdUserGuardianRide:String = "26_10";
      
      public function RiderDataClient()
      {
         super();
      }
      
      public static function get instance() : RiderDataClient
      {
         if(!_instance)
         {
            _instance = new RiderDataClient();
         }
         return _instance;
      }
      
      public function tryRide(param1:int) : void
      {
         this.sendXtMessage(TRY_RIDE,{"rid":param1});
      }
      
      public function tryUnride() : void
      {
         this.sendXtMessage(TRY_UNRIDE,null);
      }
      
      public function loadAllRiders() : void
      {
         this.sendXtMessage(LOAD_ALL_RIDERS,null);
      }
      
      public function loadAllLicense() : void
      {
         this.sendXtMessage(LOAD_ALL_LICENSE,null);
      }
      
      public function discardRider(param1:int) : void
      {
         this.sendXtMessage(ON_RIDE_PET_UPDATE,{"rid":param1});
      }
      
      public function changeDefaultRider(param1:int) : void
      {
         this.sendXtMessage(CHANGE_FIRST_RIDER,{"rid":param1});
      }
      
      public function getRidingPet() : void
      {
         this.sendXtMessage(GetRidingPet,null);
      }
      
      public function setGuardianRide(param1:int) : void
      {
         this.sendXtMessage(SetGuardianRide,{"rid":param1});
      }
      
      public function getUserGuardianRide(param1:int) : void
      {
         this.sendXtMessage(CmdUserGuardianRide,{"cid":param1});
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,param1,param2);
      }
   }
}

