package mmo.framework.comm.statistics
{
   import mmo.framework.comm.SocketClient;
   
   public class StatClient
   {
      private static var EXT_NAME:String = "SurveyExtension";
      
      private static var cmdRecordPanel:String = "1-2";
      
      private static var cmdSpcialPanel:String = "1-12";
      
      private static var cmdUserBehaviorPanel:String = "1-14";
      
      private static var CMD_ROAD_TO_LV_UP_CLICK:String = "1-18";
      
      public static const WEEKLY_PANEL:int = 31;
      
      public static const DRAGON_CALL:int = 32;
      
      public static const RAINBOW_QUEST:int = 33;
      
      public static const SHOU_HU_TUAN_XUAN_SHANG:int = 34;
      
      public static const MAIN_TASK_PANEL:int = 35;
      
      public static const SUB_TASK_NOTEBOOK:int = 36;
      
      public static const SUB_TASK_ACTIVITY:int = 37;
      
      public static const MUTE:int = 38;
      
      public static const HIDE_OTHERS:int = 39;
      
      public static const USE_FAQ:int = 40;
      
      public static const OPEN_MAP:int = 41;
      
      public static const TREASURE_OF_KING:int = 42;
      
      public static const VIEW_OTHERS_USERCARD:int = 43;
      
      public static const PLAY_NEWBIE_MOVIE:int = 205;
      
      public static const SKIP_NEWBIE_MOVIE:int = 206;
      
      public static const NEWBIE_MOVIE_END:int = 207;
      
      public static const CLICK_OH_NO_DIALOG:int = 208;
      
      public static const CLICK_PET_LOOK_AT_ME:int = 209;
      
      public static const DEFEAT_THREE_ENERMY:int = 210;
      
      public static const CHOOSE_FIRE_PET:int = 211;
      
      public static const CHOOSE_WATER_PET:int = 212;
      
      public static const CHOOSE_RABBIT_PET:int = 213;
      
      public static const USE_DRAGON_POWER:int = 214;
      
      public static const DEFEAT_SA_LU:int = 215;
      
      public static const TALK_WITH_GLANT:int = 216;
      
      public static const CHOOSE_NEW_PLAYER:int = 217;
      
      public static const CHOOSE_OLD_PLAYER:int = 218;
      
      public static const CLICK_WOUNDED_XIAO_XIA:int = 219;
      
      public static const OPEN_WORLD_BOOK:int = 220;
      
      public static const FLIP_WORLD_BOOK:int = 221;
      
      public static const CLOSED_WORLD_BOOK:int = 222;
      
      public static const CLICK_MAP:int = 223;
      
      public static const OPEN_SKILL_PANEL:int = 224;
      
      public static const CLOSE_SKILL_PANEL:int = 225;
      
      public static const FLIP_FIGHT_BOOK:int = 226;
      
      public static const CLOSE_FIGHT_BOOK:int = 227;
      
      public static const OPEN_EQUIP_SALE:int = 228;
      
      public static const CLOSE_EQUIP_SALE:int = 229;
      
      public static const FLIP_EQUIP_BOOK:int = 230;
      
      public static const CLOSE_EQUIP_BOOK:int = 231;
      
      public static const TALK_WITH_KA_NA:int = 232;
      
      public static const PROMISE_PROTECT_ASLAN:int = 233;
      
      public static const CLICK_VIP_NEWS:int = 295;
      
      public static const CLICK_ADDBUDDY_CHARACTERPANEL:int = 297;
      
      public static const CLICK_LOCATEBUUDY_CHARACTERPANEL:int = 298;
      
      public static const CLICK_TEAM_CHARACTERPANEL:int = 299;
      
      public static const CLICK_PVP_CHARACTERPANEL:int = 300;
      
      public static const CLICK_BLOCKNAME_CHARACTERPANEL:int = 301;
      
      public static const CLICK_REPORT_CHARACTERPANEL:int = 302;
      
      public static const CLICK_MAN_WARRIOR:int = 473;
      
      public static const CLICK_WOMAN_WARRIOR:int = 474;
      
      public static const CLICK_MAN_ARCHER:int = 475;
      
      public static const CLICK_WOMAN_ARCHER:int = 476;
      
      public static const CLICK_CHOOSE_FIRST_CLOTHES:int = 477;
      
      public static const CLICK_CHOOSE_SECOND_CLOTHES:int = 478;
      
      public static const CLICK_CHOOSE_THIRD_CLOTHES:int = 479;
      
      public static const CLICK_INPUI_NAME:int = 480;
      
      public static const NAME_REPEAT:int = 481;
      
      public static const CLICK_MALL_ICON:int = 482;
      
      public static const CLICK_MALL_HOT_TAB:int = 483;
      
      public static const CLICK_MALL_RIDDERPET_TAB:int = 484;
      
      public static const CLICK_MALL_MATERIAL_TAB:int = 485;
      
      public static const CLICK_MALL_CLOTH_TAB:int = 486;
      
      public static const CLICK_MALL_HOT_RECOMDED_TAB:int = 487;
      
      public static const CLICK_MALL_HOT_RIDDER_TAB:int = 488;
      
      public static const CLICK_MALL_HOT_MATERIAL_TAB:int = 489;
      
      public static const CLICK_MALL_HOT_CLOTH_TAB:int = 490;
      
      public static const CLICK_MALL_RIDDERPET_PET_TAB:int = 491;
      
      public static const CLICK_MALL_RIDDERPET_RIDDER_TAB:int = 492;
      
      public static const CLICK_MALL_MATERIAL_CONSUMER_TAB:int = 493;
      
      public static const CLICK_MALL_MATERIAL_ITEM_TAB:int = 494;
      
      public static const CLICK_MALL_MATERIAL_LIBAO:int = 495;
      
      public static const CLICK_MALL_CLOTH_EQUIPMENT:int = 496;
      
      public static const CLICK_MALL_CLOTH_CLOTH:int = 497;
      
      public static const CLICK_MALL_CLOTH_DECORATE:int = 498;
      
      public static const CLICK_MALL_CLOTH_FACE:int = 499;
      
      public static const CLICK_MALL_CLOTH_BACKGROUND:int = 500;
      
      public static const CLICK_MALL_CLOTH_SUIT:int = 501;
      
      public static const CLICK_SECRET_SHOP:int = 536;
      
      public static const CLICK_YAYA_PANEL:int = 576;
      
      public static const CLICK_QINGWQ_TV:int = 581;
      
      public static const CLICK_OUT_OF_PRINT_PANEL:int = 727;
      
      public static const CLICK_DICTIONARY_PANEL:int = 744;
      
      public static const CLICK_WONDERFULACTIVITY_PANEL:int = 745;
      
      public static const CLICK_CHAT_PANEL:int = 746;
      
      public static const CLICK_BAG_PANEL:int = 747;
      
      public static const CLICK_SKILL_PANEL:int = 748;
      
      public static const CLICK_FRIEND_PANEL:int = 749;
      
      public static const CLICK_FAMILY_PANEL:int = 750;
      
      public static const CLICK_TEAM_PANEL:int = 751;
      
      public static const CLICK_RIDE_PANEL:int = 752;
      
      public static const CLICK_PET_PANEL:int = 753;
      
      public static const CLICK_DRAGON_RIDER_PERSON_INFO:int = 754;
      
      public static const CLICK_DRAGON_RIDER_PREROGATIVE:int = 755;
      
      public static const CLICK_DRAGON_RIDER_ANNUAL_PREROGATIVE:int = 756;
      
      public static const CLICK_DRAGON_RIDER_ANNUAL_GET_AWARD:int = 757;
      
      public static const CLICK_FIGHT_BOOK:int = 758;
      
      public static const CLICK_EQUIP_BOOK:int = 759;
      
      public static const CLICK_PET_BOOK:int = 760;
      
      public static const CLICK_WORLD_VIEW_BOOK:int = 761;
      
      public static const CLICK_FAMILY_HELPER_BOOK:int = 762;
      
      public static const CLICK_DRAGON_ASSISTANT_ASSISTANT:int = 763;
      
      public static const CLICK_DRAGON_ASSISTANT_EQUIP:int = 764;
      
      public static const CLICK_DRAGON_ASSISTANT_EMAIL:int = 765;
      
      public static const CLICK_DRAGON_ASSISTANT_ENCYCLOPAEDIA:int = 766;
      
      public static const CLICK_HERO_RANK_FLOWER:int = 767;
      
      public static const CLICK_HERO_RANK_RICH:int = 768;
      
      public static const CLICK_HERO_RANK_EQUIP:int = 769;
      
      public static const CLICK_HERO_RANK_DEFEND:int = 770;
      
      public static const CLICK_HERO_RANK_FAMILY_REPUTATION:int = 771;
      
      public static const CLICK_HERO_RANK_LUCIFA:int = 772;
      
      public static const CLICK_HERO_RANK_SAGUSY:int = 773;
      
      public static const CLICK_HERO_RANK_FIRST_HUNDRED:int = 774;
      
      public static const CLICK_HERO_RANK_SECEND_HUNDRED:int = 775;
      
      public static const CLICK_HERO_RANK_ATHLETICS:int = 776;
      
      public static const CLICK_ANNUAL_AMBASSADOR:int = 807;
      
      public static const CLICK_ANNUAL_LEVELUP:int = 808;
      
      public static const CLICK_COPY_PANEL:int = 809;
      
      public static const CLICK_PET_GAME:int = 810;
      
      public static const CLICK_CARTOON:int = 811;
      
      public static const CLICK_BUY_IN_LIMIT:int = 815;
      
      public static const CLICK_PUSH_TREE:int = 816;
      
      public static const CLICK_CANDYSENDER:int = 817;
      
      public static const CLICK_FXH_EXCHANGE:int = 818;
      
      public static const CLICK_KINGPET:int = 819;
      
      public static const CLICK_VIP_IN_YEAR:int = 820;
      
      public static const CLICK_DRAGON_FLAG:int = 821;
      
      public static const CLICK_VIP_ACTIVE_PANEL:int = 823;
      
      public static const CLICK_THREE_COMPETITION_PANEL:int = 824;
      
      public static const CLICK_MOTHERDAY_PANEL:int = 825;
      
      public static const CHILDRENDAY_PANEL:int = 839;
      
      public static const CLICK_LDS_MOVIE:int = 840;
      
      public static const CLICK_LDS_MOVIE_BONUS:int = 849;
      
      public static const CLICK_LDS_DRAGON_TREE_HELP:int = 861;
      
      public static const CLICK_PREDICT_STONE_PANEL:int = 868;
      
      public static const CLICK_SUMMER_ACTIVITY_PANEL:int = 882;
      
      public static const CLICK_HUD_MALL_ICON:int = 883;
      
      public static const CLICK_VIP_ICON:int = 884;
      
      public static const CLICK_GAME_ICON:int = 885;
      
      public static const CLICK_SKILL_TIPS_ICON:int = 886;
      
      public static const CLICK_DRAGON_ADVENTURE_ICON:int = 887;
      
      public static const CLICK_DRAGON_TREE_ICON:int = 888;
      
      public static const CLICK_MAIL_TIPS_ICON:int = 889;
      
      public static const CLICK_EQUIP_FIX_TIPS_ICON:int = 890;
      
      public static const CLICK_TAITAN_CHANLENGE:int = 893;
      
      public static const CLICK_TIME_WAR:int = 894;
      
      public static const CLICK_BTN_TIME_SEARCH:int = 899;
      
      public static const CLICK_MALL_TIME_WHEEL:int = 900;
      
      public static const CLICK_HUD_HADIS:int = 907;
      
      public static const CLICK_BULOLY_ASSAULT:int = 908;
      
      public static const CLICK_TREASURE_ARMS:int = 909;
      
      public static const CLICK_HADIS_SCENE_ITEM:int = 910;
      
      public static const CLICK_GOD_CONCENTRATE_ITEM:int = 911;
      
      public static const CLICK_HERO_GO_FORWARD:int = 912;
      
      public static const CLICK_INTEGRATION_PANEL_BULOLY:int = 913;
      
      public static const CLICK_INTEGRATION_PANEL_TREASURE_ARMS:int = 914;
      
      public static const CLICK_INTEGRATION_PANEL_HADIS:int = 915;
      
      public static const CLICK_INTEGRATION_PANEL_GOD_CONCENTRATE:int = 916;
      
      public static const CLICK_INTEGRATION_PANEL_HERO_GO_FORWARD:int = 917;
      
      public static const CLICK_BOOK_FESTIVAL_JOIN:int = 918;
      
      public static const CLICK_MALL_GIFT_MALL_INTERFACES:int = 919;
      
      public static const CLICK_WILLPOWER_TEST_SCENE_ITEM:int = 920;
      
      public static const CLICK_WILLPOWER_TEST_INTEGRATION_PANEL:int = 921;
      
      public static const CLICK_COURAGE_TEST_SCENE_ITEM:int = 922;
      
      public static const CLICK_COURAGE_TEST_INTEGRATION_PANEL:int = 923;
      
      public static const CLICK_SHENLO_SCENE_ITEM:int = 924;
      
      public static const CLICK_SHENLO_INTEGRATION_PANEL:int = 925;
      
      public static const CLICK_DRAGON_KING_GIFT:int = 926;
      
      public static const CLICK_DRAGON_KING_GIFT_INTEGRATION_PANEL:int = 927;
      
      public static const CLICK_STRENTHEN_PANEL:int = 948;
      
      public static const CLICK_VIP_ANNIVERSARY:int = 967;
      
      public static const HIT_FROM_CLIENT:int = 2;
      
      public static const KEYBOARD_DOWN:int = 3;
      
      public static const WORK_DOMAIN:int = 4;
      
      public static const EQUIP_ACHIEVE:int = 54;
      
      public static const EQUIP_BUY:int = 55;
      
      public static const PET_ACHIEVE:int = 56;
      
      public static const PET_BUY_JIN_BI:int = 57;
      
      public static const PET_BUY_LONG_BI:int = 58;
      
      public static const PET_EVOLUTION:int = 59;
      
      public function StatClient()
      {
         super();
      }
      
      public static function recordPanel(param1:int) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,cmdRecordPanel,{"id":param1});
      }
      
      public static function recordUserBehavior(param1:String) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,cmdUserBehaviorPanel,{
            "id":parseInt(param1.split("_")[0]),
            "si":param1.split("_")[1]
         });
      }
      
      public static function recordUserBehaviorEx(param1:String, param2:int = 1, param3:Object = null) : void
      {
         var _loc4_:Object = {
            "id":parseInt(param1.split("_")[0]),
            "si":param1.split("_")[1],
            "num":param2,
            "ex":param3
         };
         SocketClient.instance.sendXtMessage(EXT_NAME,cmdUserBehaviorPanel,_loc4_);
      }
      
      public static function statSpecial(param1:int, param2:String) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,cmdSpcialPanel,{
            "id":param1,
            "e":param2
         });
      }
      
      public static function recordClickCount(param1:int, param2:int) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,CMD_ROAD_TO_LV_UP_CLICK,{
            "ai":param1,
            "s":param2
         });
      }
   }
}

