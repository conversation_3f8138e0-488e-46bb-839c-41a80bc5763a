package mmo.framework.comm.store
{
   import mmo.framework.comm.SocketClient;
   
   public class StoreClient
   {
      private static var _instance:StoreClient;
      
      private static const EXTENSION_NAME:String = "StoreExtension";
      
      public static const BUY_SINGLE:String = "21_1";
      
      public function StoreClient()
      {
         super();
      }
      
      public static function get instance() : StoreClient
      {
         if(!_instance)
         {
            _instance = new StoreClient();
         }
         return _instance;
      }
      
      public function buySingle(param1:int, param2:int, param3:int) : void
      {
         this.sendXtMessage(BUY_SINGLE,{
            "id":param2,
            "exp":param3
         });
      }
      
      private function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXTENSION_NAME,param1,param2);
      }
   }
}

