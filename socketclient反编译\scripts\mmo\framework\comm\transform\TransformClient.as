package mmo.framework.comm.transform
{
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   
   public class TransformClient
   {
      public static const CMD_LOAD_USER_INFO:* = "83_1";
      
      public static const CMD_VIEW_USER_INFO:* = "83_2";
      
      public static const CMD_SELECT_TOTEM:* = "83_6";
      
      public static const CMD_TOTEM_UPGRADE_CLASS:* = "83_7";
      
      public static const CMD_TOTEM_GAIN_EXP:* = "83_8";
      
      public static const CMD_START_TRANSFIGURATION:* = "83_21";
      
      public static const CMD_CANCEL_TRANSFIGURATION:* = "83_23";
      
      public static const RSP_START_TRANSFIGURATION:* = "83_22";
      
      public static const RSP_CANCEL_TRANSFIGURATION:* = "83_24";
      
      public static const CMD_ADD_ENERGY_WITH_LONG_BI:* = "83_11";
      
      public static const RSP_UPDATE_ENERGY:* = "83_16";
      
      public static const RSP_ADD_TOTEM:* = "83_18";
      
      public function TransformClient()
      {
         super();
      }
      
      public static function loadInfo() : void
      {
         sendXtMessage(CMD_LOAD_USER_INFO);
      }
      
      public static function getUserInfo() : void
      {
         sendXtMessage(CMD_VIEW_USER_INFO);
      }
      
      public static function selectTotem(param1:int) : void
      {
         sendXtMessage(CMD_SELECT_TOTEM,{"t":param1});
      }
      
      public static function totemGainExp(param1:int, param2:int) : void
      {
         sendXtMessage(CMD_TOTEM_GAIN_EXP,{
            "t":param1,
            "i":param2
         });
      }
      
      public static function totemClassUp(param1:int) : void
      {
         sendXtMessage(CMD_TOTEM_UPGRADE_CLASS,{"t":param1});
      }
      
      public static function startTransform() : void
      {
         sendXtMessage(CMD_START_TRANSFIGURATION,{});
      }
      
      public static function startTransformTrially(param1:int) : void
      {
         sendXtMessage(CMD_START_TRANSFIGURATION,{"t":param1});
      }
      
      public static function cancelTransform() : void
      {
         sendXtMessage(CMD_CANCEL_TRANSFIGURATION);
      }
      
      public static function addEnergyWithLongbi() : void
      {
         sendXtMessage(CMD_ADD_ENERGY_WITH_LONG_BI);
      }
      
      private static function sendXtMessage(param1:String, param2:Object = null) : *
      {
         SocketClient.instance.sendXtMessage(ExtMap.TransformExtension,param1,param2);
      }
   }
}

