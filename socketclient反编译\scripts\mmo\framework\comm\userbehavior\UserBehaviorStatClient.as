package mmo.framework.comm.userbehavior
{
   import mmo.common.DateUtil;
   import mmo.common.user.UserInfo;
   import mmo.framework.comm.ExtMap;
   import mmo.framework.comm.SocketClient;
   import mmo.framework.comm.SocketClientEvent;
   
   public class UserBehaviorStatClient
   {
      private static const NOTIFY_CMD_INIT_DATA:String = "25_967_1";
      
      private static const CMD_INC_BEHAVIORLABEL_LENGTH:String = "25_967_2";
      
      private static const CMD_RECORD_ACTIVITY_TIMES:String = "25_967_3";
      
      private static var _object:Object = {};
      
      private static var isMaxBaCharacterId:Boolean = false;
      
      public function UserBehaviorStatClient()
      {
         super();
      }
      
      public static function notifyServerInitData() : void
      {
         var callBack:Function = null;
         callBack = function(param1:SocketClientEvent):void
         {
            isMaxBaCharacterId = UserInfo.characterId == param1.params.cid;
         };
         sendMsg(NOTIFY_CMD_INIT_DATA,null,callBack);
      }
      
      public static function startActivityTime(param1:int) : void
      {
         _object[param1.toString()] = DateUtil.getServerTimeInMS();
      }
      
      public static function endActivityTime(param1:int) : void
      {
         if(!_object.hasOwnProperty(param1.toString()))
         {
            return;
         }
         var _loc2_:Number = Number(_object[param1.toString()]);
         delete _object[param1.toString()];
         var _loc3_:int = (DateUtil.getServerTimeInMS() - _loc2_) / 1000;
         if(_loc3_ <= 0)
         {
            return;
         }
         if(_loc3_ > 18000)
         {
            _loc3_ = 18000;
         }
         sendMsg(CMD_INC_BEHAVIORLABEL_LENGTH,{
            "id":param1,
            "inc":_loc3_
         },null);
      }
      
      public static function recordActivityTimes(param1:int) : void
      {
         sendMsg(CMD_RECORD_ACTIVITY_TIMES,{"id":param1},null);
      }
      
      public static function hasExistId(param1:int) : Boolean
      {
         return _object.hasOwnProperty(param1.toString());
      }
      
      private static function sendMsg(param1:String, param2:Object, param3:Function) : void
      {
         SocketClient.instance.sendXtMsgWithCallBack(ExtMap.SmallGameExtension,param1,param2,param3);
      }
   }
}

