package mmo.framework.comm.usercard
{
   import mmo.framework.comm.SocketClient;
   
   public class UsercardClient
   {
      private static const EXT_NAME:String = "UsercardExtension";
      
      public static const GET_WEB_PAY_KEY:String = "31_1";
      
      public function UsercardClient()
      {
         super();
      }
      
      public static function getWebPayKey(param1:String) : void
      {
         var _loc2_:Object = {"s":param1};
         sendXtMessage(GET_WEB_PAY_KEY,_loc2_);
      }
      
      private static function sendXtMessage(param1:String, param2:Object) : void
      {
         SocketClient.instance.sendXtMessage(EXT_NAME,param1,param2);
      }
   }
}

