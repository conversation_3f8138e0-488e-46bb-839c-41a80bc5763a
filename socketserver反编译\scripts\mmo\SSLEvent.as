package mmo
{
   import flash.events.Event;
   
   public class SSLEvent extends Event
   {
      public static const onLogin:String = "onLogin";
      
      public static const onLogout:String = "onLogout";
      
      public var params:Object;
      
      public function SSLEvent(param1:String, param2:Object)
      {
         super(param1);
         this.params = param2;
      }
      
      override public function clone() : Event
      {
         return new SSLEvent(this.type,this.params);
      }
      
      override public function toString() : String
      {
         return formatToString("SSLEvent","type","bubbles","cancelable","eventPhase","params");
      }
   }
}

