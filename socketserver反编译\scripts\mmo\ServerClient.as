package mmo
{
   import flash.display.Sprite;
   import mmo.config.ConfigReader;
   import mmo.socketserver.MsgSeq;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   
   public class ServerClient extends Sprite
   {
      private static var _instance:ServerClient;
      
      private var client:SocketServerClient;
      
      private var curServerInfo:ServerInfo;
      
      private var loadingServerInfo:ServerInfo;
      
      public var userName:String;
      
      public var lastLoginTime:Number;
      
      private var password:String;
      
      public function ServerClient()
      {
         super();
         _instance = this;
      }
      
      public static function get instance() : ServerClient
      {
         return _instance;
      }
      
      public function get serverIP() : String
      {
         return this.curServerInfo.serverIP;
      }
      
      public function get port() : int
      {
         return this.curServerInfo.port;
      }
      
      public function get zone() : String
      {
         return this.curServerInfo.zone;
      }
      
      public function get socketServerClient() : SocketServerClient
      {
         return this.client;
      }
      
      public function disconnect() : void
      {
         if(this.client != null && this.client.isConnected)
         {
            if(this.client.myUserName != "")
            {
               this.client.disconnect();
            }
         }
      }
      
      public function login(param1:String, param2:int, param3:String, param4:String, param5:String, param6:Boolean = false) : void
      {
         this.loadingServerInfo = new ServerInfo();
         this.loadingServerInfo.serverIP = param1;
         this.loadingServerInfo.port = param2;
         this.loadingServerInfo.zone = param3;
         this.userName = param4;
         this.password = param5;
         if(this.client == null)
         {
            this.client = new SocketServerClient(ConfigReader.instance.socketDebug);
            this.setListener(true);
            this.client.connect(this.loadingServerInfo.serverIP,this.loadingServerInfo.port);
         }
         else if(!this.client.isConnected)
         {
            this.setListener(true);
            this.client.connect(this.loadingServerInfo.serverIP,this.loadingServerInfo.port);
         }
         else
         {
            this.setListener(true);
            if(this.client.myUserName == "")
            {
               this.doLogin();
            }
         }
      }
      
      private function setListener(param1:Boolean) : void
      {
         if(param1)
         {
            this.client.addEventListener(SSEvent.onConnection,this.onConnection);
            this.client.addEventListener(SSEvent.onLogin,this.onLogin);
            this.client.addEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
         }
         else
         {
            this.client.removeEventListener(SSEvent.onConnection,this.onConnection);
            this.client.removeEventListener(SSEvent.onLogin,this.onLogin);
            this.client.removeEventListener(SSEvent.onExtensionResponse,this.onExtensionResponse);
         }
      }
      
      private function doLogin() : void
      {
         this.client.login(this.loadingServerInfo.zone,this.userName,this.password);
      }
      
      private function onConnection(param1:SSEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:SSLEvent = null;
         if(!param1.params.success)
         {
            this.setListener(false);
            _loc2_ = param1.params;
            _loc2_.success = false;
            _loc2_.error = "今天龙斗士上的冒险家真多啊，交通拥挤哦！换个网速较快的浮空岛，或者等一会再试试吧！";
            _loc3_ = new SSLEvent(SSLEvent.onLogin,_loc2_);
            this.dispatchEvent(_loc3_);
         }
         else
         {
            this.doLogin();
         }
      }
      
      private function onLogin(param1:SSEvent) : void
      {
         if(param1.params.success == true)
         {
            this.curServerInfo = this.loadingServerInfo;
         }
         else
         {
            this.client.disconnect();
         }
         var _loc2_:SSLEvent = new SSLEvent(SSLEvent.onLogin,param1.params);
         this.dispatchEvent(_loc2_);
      }
      
      private function onExtensionResponse(param1:SSEvent) : void
      {
         this.setListener(false);
         var _loc2_:Object = param1.params.dataObj;
         var _loc3_:String = _loc2_._cmd;
         var _loc4_:Object = {};
         switch(_loc3_)
         {
            case "logOK":
               this.socketServerClient.myUserId = _loc2_.id;
               this.socketServerClient.myUserName = _loc2_.n;
               this.lastLoginTime = _loc2_.lastLoginTime;
               _loc4_.success = true;
               _loc4_.name = _loc2_.n;
               _loc4_.error = "";
               this.curServerInfo = this.loadingServerInfo;
               break;
            case "logKO":
               _loc4_.success = false;
               _loc4_.error = _loc2_.err;
               this.client.disconnect();
               break;
            default:
               return;
         }
         var _loc5_:SSLEvent = new SSLEvent(SSLEvent.onLogin,_loc4_);
         this.dispatchEvent(_loc5_);
      }
   }
}

