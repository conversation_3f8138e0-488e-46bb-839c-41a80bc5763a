package mmo.socketserver
{
   import flash.utils.ByteArray;
   import mmo.common.eventdispatcher.CommonEvent;
   import mmo.common.eventdispatcher.CommonEventDispatcher;
   
   public class LdsMsgEncrypter
   {
      public function LdsMsgEncrypter()
      {
         super();
      }
      
      public function e(param1:int, param2:ByteArray) : *
      {
         CommonEventDispatcher.instance.dispatchEvent(new CommonEvent("come_and_get_me",{
            "seq":param1,
            "data":param2
         }));
      }
   }
}

