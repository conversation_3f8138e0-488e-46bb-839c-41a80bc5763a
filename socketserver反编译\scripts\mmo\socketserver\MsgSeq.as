package mmo.socketserver
{
   import mmo.common.eventdispatcher.CommonEvent;
   import mmo.common.eventdispatcher.CommonEventDispatcher;
   
   public class MsgSeq
   {
      public function MsgSeq()
      {
         super();
      }
      
      public function next(param1:int, param2:int) : int
      {
         var _loc3_:Object = {
            "p1":param1,
            "p2":param2
         };
         CommonEventDispatcher.instance.dispatchEvent(new CommonEvent("damn_paris",_loc3_));
         return _loc3_.re;
      }
   }
}

