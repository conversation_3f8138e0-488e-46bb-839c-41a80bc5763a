package mmo.socketserver
{
   import flash.events.Event;
   
   public class SSEvent extends Event
   {
      public static const onAdminMessage:String = "onAdminMessage";
      
      public static const onConnection:String = "onConnection";
      
      public static const onConnectionLost:String = "onConnectionLost";
      
      public static const onCreateRoomError:String = "onCreateRoomError";
      
      public static const onDebugMessage:String = "onDebugMessage";
      
      public static const onExtensionResponse:String = "onExtensionResponse";
      
      public static const onExtensionResponseByte:String = "onExtensionResponseByte";
      
      public static const onJoinRoom:String = "onJoinRoom";
      
      public static const onJoinRoomError:String = "onJoinRoomError";
      
      public static const onLogin:String = "onLogin";
      
      public static const onObjectReceived:String = "onObjectReceived";
      
      public static const onPublicMessage:String = "onPublicMessage";
      
      public static const onRoomLeft:String = "onRoomLeft";
      
      public static const onRoomListUpdate:String = "onRoomListUpdate";
      
      public static const onRoomVariablesUpdate:String = "onRoomVariablesUpdate";
      
      public static const onUserEnterRoom:String = "onUserEnterRoom";
      
      public static const onUserLeaveRoom:String = "onUserLeaveRoom";
      
      public static const onUserVariablesUpdate:String = "onUserVariablesUpdate";
      
      public static const onExtMessageSent:String = "onExtMessageSent";
      
      public static const onExtByteMessageSent:String = "onExtByteMessageSent";
      
      public var params:Object;
      
      public function SSEvent(param1:String, param2:Object)
      {
         super(param1);
         this.params = param2;
      }
      
      override public function clone() : Event
      {
         return new SSEvent(this.type,this.params);
      }
      
      override public function toString() : String
      {
         return formatToString("SSEvent","type","bubbles","cancelable","eventPhase","params");
      }
   }
}

