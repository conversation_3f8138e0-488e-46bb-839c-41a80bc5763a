package mmo.socketserver
{
   import flash.events.ErrorEvent;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.Socket;
   import flash.utils.ByteArray;
   import mmo.socketserver.data.Room;
   import mmo.socketserver.data.User;
   import mmo.socketserver.handlers.ExtBHandler;
   import mmo.socketserver.handlers.ExtHandler;
   import mmo.socketserver.handlers.IMessageHandler;
   import mmo.socketserver.handlers.SysHandler;
   import mmo.socketserver.protocol.BinaryMsgWriter;
   import mmo.socketserver.protocol.ExMsgCodec;
   import mmo.socketserver.protocol.IMsgWriter;
   
   public class SocketServerClient extends EventDispatcher
   {
      private var connected:Boolean;
      
      private var sysHandler:SysHandler;
      
      private var extHandler:ExtHandler;
      
      private var messageHandlers:Array;
      
      private var socketConnection:Socket;
      
      private var byteBuffer:ByteArray;
      
      private var autoConnectOnConfigSuccess:Boolean = false;
      
      public var ipAddress:String;
      
      public var port:int = 9339;
      
      public var defaultZone:String;
      
      public var debug:Boolean;
      
      public var myUserId:int;
      
      public var myUserName:String;
      
      public var zoneName:String;
      
      public var activeRoomId:int;
      
      public var activeRoom:Room;
      
      public var changingRoom:Boolean;
      
      private var lastOperationTime:Date;
      
      private var handledPolicyFile:Boolean;
      
      private var waitingForHeader:Boolean;
      
      private var bytesNeeded:int;
      
      private var msgWriter:IMsgWriter;
      
      private var msgSeqGen:Object;
      
      private var msgEncrypter:Object = new LdsMsgEncrypter();
      
      public var exProtocal:String;
      
      private var packetNum:int = -1;
      
      public function SocketServerClient(param1:Boolean = false, param2:Object = null)
      {
         super();
         this.activeRoomId = -1;
         this.debug = param1;
         if(param2 != null)
         {
            this.msgSeqGen = param2;
         }
         else
         {
            this.msgSeqGen = new DefaultMsgSeq();
         }
         this.packetNum = -1;
         this.messageHandlers = [];
         this.setupMessageHandlers();
         this.setMsgWriter();
         this.socketConnection = new Socket();
         this.socketConnection.addEventListener(Event.CONNECT,this.handleSocketConnection);
         this.socketConnection.addEventListener(Event.CLOSE,this.handleSocketDisconnection);
         this.socketConnection.addEventListener(ProgressEvent.SOCKET_DATA,this.handleSocketData);
         this.socketConnection.addEventListener(IOErrorEvent.IO_ERROR,this.handleIOError);
         this.socketConnection.addEventListener(IOErrorEvent.NETWORK_ERROR,this.handleIOError);
         this.socketConnection.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.handleSecurityError);
         this.byteBuffer = new ByteArray();
         this.updateOperationTime();
      }
      
      public function setMsgEncrypter(param1:Object) : void
      {
         this.msgEncrypter = param1;
      }
      
      private function setMsgWriter() : void
      {
         if(this.msgWriter != null)
         {
            return;
         }
         this.msgWriter = new BinaryMsgWriter();
         this.msgWriter.setServer(this);
      }
      
      public function get isConnected() : Boolean
      {
         return this.connected;
      }
      
      public function set isConnected(param1:Boolean) : void
      {
         this.connected = param1;
      }
      
      public function connect(param1:String, param2:int = 9339) : void
      {
         if(!this.connected)
         {
            this.initialize();
            this.ipAddress = param1;
            this.port = param2;
            ExMsgCodec.initExMsgCodec();
            this.socketConnection.connect(param1,param2);
         }
         else
         {
            this.debugMessage("*** ALREADY CONNECTED ***");
         }
      }
      
      public function disconnect() : void
      {
         this.connected = false;
         this.socketConnection.close();
         this.sysHandler.dispatchDisconnection();
      }
      
      public function getActiveRoom() : Room
      {
         return this.activeRoom;
      }
      
      public function joinRoom(param1:String, param2:String = "", param3:Boolean = false, param4:Boolean = false, param5:Boolean = false, param6:int = -1, param7:String = "") : void
      {
         if(!this.changingRoom)
         {
            this.msgWriter.joinRoom(param1,param4,param5,param6,param7);
            this.changingRoom = true;
         }
      }
      
      public function leaveRoom(param1:int) : void
      {
         this.msgWriter.leaveRoom(param1);
      }
      
      public function login(param1:String, param2:String, param3:String) : void
      {
         this.zoneName = param1;
         this.msgWriter.login(param1,param2,param3);
      }
      
      public function hit() : void
      {
         this.msgWriter.hit();
         this.updateOperationTime();
      }
      
      public function sendPublicMessage(param1:String, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.sendPublicMessage(param1,param2);
         this.updateOperationTime();
      }
      
      public function sendObject(param1:Object, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.sendObject(param1,param2);
      }
      
      public function sendXtMessage(param1:int, param2:String, param3:*, param4:String = "xml", param5:int = -1) : void
      {
         if(param5 == -1)
         {
            param5 = this.activeRoomId;
         }
         this.updateOperationTime();
         var _loc6_:Object = {
            "id":param1,
            "cmd":param2,
            "param":param3
         };
         this.msgWriter.sendXtMessage(param5,_loc6_);
         if(this.debug)
         {
            dispatchEvent(new SSEvent(SSEvent.onExtMessageSent,{
               "cmd":param2,
               "param":param3
            }));
         }
      }
      
      public function sendXtMessageByte(param1:int, param2:ByteArray, param3:int = -1) : void
      {
         if(param3 == -1)
         {
            param3 = this.activeRoomId;
         }
         this.updateOperationTime();
         var _loc4_:ByteArray = new ByteArray();
         _loc4_.writeByte(param1);
         _loc4_.writeBytes(param2,0);
         this.msgWriter.sendXtMessageByte(param3,_loc4_);
         if(this.debug)
         {
            dispatchEvent(new SSEvent(SSEvent.onExtByteMessageSent,{"byteData":param2}));
         }
      }
      
      public function setRoomVariables(param1:Array, param2:int = -1, param3:Boolean = true) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         this.msgWriter.setRoomVariables(param1,param2,param3);
         this.updateOperationTime();
      }
      
      public function setUserVariables(param1:Object, param2:int = -1) : void
      {
         if(param2 == -1)
         {
            param2 = this.activeRoomId;
         }
         var _loc3_:Room = this.getActiveRoom();
         var _loc4_:User = _loc3_.getUser(this.myUserId);
         _loc4_.setVariables(param1);
         this.msgWriter.setUserVariables(param1,param2);
         this.updateOperationTime();
      }
      
      private function initialize() : void
      {
         this.changingRoom = false;
         this.activeRoom = null;
         this.activeRoomId = -1;
         this.myUserId = -1;
         this.myUserName = "";
         this.connected = false;
         this.handledPolicyFile = false;
         this.packetNum = -1;
         this.waitingForHeader = true;
      }
      
      private function setupMessageHandlers() : void
      {
         if(this.sysHandler != null)
         {
            return;
         }
         this.sysHandler = new SysHandler(this);
         this.extHandler = new ExtHandler(this);
         this.addMessageHandler(0,this.sysHandler);
         this.addMessageHandler(1,this.extHandler);
         this.addMessageHandler(2,new ExtBHandler(this));
      }
      
      private function addMessageHandler(param1:int, param2:IMessageHandler) : void
      {
         this.messageHandlers[param1] = param2;
      }
      
      public function debugMessage(param1:String) : void
      {
         var _loc2_:SSEvent = null;
         if(this.debug)
         {
            _loc2_ = new SSEvent(SSEvent.onDebugMessage,{"message":param1});
            dispatchEvent(_loc2_);
         }
      }
      
      public function writeBinaryToSocket(param1:ByteArray) : void
      {
         this.socketConnection.writeInt(param1.length + 4);
         var _loc2_:int = this.genPacketNum();
         this.socketConnection.writeInt(_loc2_);
         if(_loc2_ != 0)
         {
            this.msgEncrypter.e(_loc2_,param1);
         }
         this.socketConnection.writeBytes(param1);
         this.socketConnection.flush();
      }
      
      private function genPacketNum() : int
      {
         if(this.packetNum == -1)
         {
            this.packetNum = 0;
         }
         else
         {
            this.packetNum = this.msgSeqGen.next(this.packetNum,this.myUserId > 0 ? this.myUserId : 0);
         }
         return this.packetNum;
      }
      
      private function handleSocketConnection(param1:Event) : void
      {
         this.isConnected = true;
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnection,{"success":true});
         this.dispatchEvent(_loc2_);
      }
      
      private function handleSocketDisconnection(param1:Event) : void
      {
         this.initialize();
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnectionLost,{});
         dispatchEvent(_loc2_);
      }
      
      private function handleIOError(param1:IOErrorEvent) : void
      {
         this.handleConnectionError(param1);
      }
      
      private function handleConnectionError(param1:ErrorEvent) : void
      {
         if(!this.connected)
         {
            this.dispatchConnectionError();
         }
         else
         {
            dispatchEvent(param1);
            this.debugMessage("[WARN] Connection error: " + param1.text);
         }
      }
      
      private function handleSocketError(param1:SecurityErrorEvent) : void
      {
         this.debugMessage("Socket Error: " + param1.text);
      }
      
      private function handleSecurityError(param1:SecurityErrorEvent) : void
      {
         this.handleConnectionError(param1);
      }
      
      private function handleSocketData(param1:Event) : void
      {
         this.onBinarySocketData();
      }
      
      private function onBinarySocketData() : void
      {
         if(!this.handledPolicyFile)
         {
            if(this.socketConnection.readUTFBytes(1) == "<")
            {
               while(this.socketConnection.readByte() != 0)
               {
               }
               this.handledPolicyFile = true;
            }
         }
         this.processBinarySocketData();
      }
      
      private function processBinarySocketData() : void
      {
         var _loc1_:ByteArray = null;
         if(!this.isConnected)
         {
            return;
         }
         if(this.waitingForHeader)
         {
            if(this.socketConnection.bytesAvailable >= 4)
            {
               this.bytesNeeded = this.socketConnection.readInt();
               this.waitingForHeader = false;
            }
         }
         if(!this.waitingForHeader)
         {
            if(this.socketConnection.bytesAvailable >= this.bytesNeeded)
            {
               _loc1_ = new ByteArray();
               this.socketConnection.readBytes(_loc1_,0,this.bytesNeeded);
               this.handleBinaryMessage(_loc1_);
               this.waitingForHeader = true;
               this.processBinarySocketData();
            }
         }
      }
      
      private function handleBinaryMessage(param1:ByteArray) : void
      {
         var _loc2_:int = param1.readByte();
         var _loc3_:IMessageHandler = this.messageHandlers[_loc2_];
         if(_loc3_ != null)
         {
            _loc3_.handleMessage(param1);
         }
      }
      
      private function dispatchConnectionError() : void
      {
         var _loc1_:Object = {};
         _loc1_.success = false;
         _loc1_.error = "I/O Error";
         var _loc2_:SSEvent = new SSEvent(SSEvent.onConnection,_loc1_);
         dispatchEvent(_loc2_);
      }
      
      private function updateOperationTime() : void
      {
         this.lastOperationTime = new Date();
      }
      
      public function isIdle(param1:int = 1800) : Boolean
      {
         var _loc2_:Date = new Date();
         return _loc2_.time >= this.lastOperationTime.time + param1 * 1000;
      }
   }
}

