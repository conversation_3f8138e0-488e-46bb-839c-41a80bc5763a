package mmo.socketserver.data
{
   public class Room
   {
      private var id:int;
      
      private var name:String;
      
      private var maxUsers:int;
      
      private var userCount:int;
      
      private var userList:Array;
      
      private var variables:Array;
      
      public function Room(param1:int, param2:String, param3:int, param4:int = 0)
      {
         super();
         this.id = param1;
         this.name = param2;
         this.maxUsers = param3;
         this.userCount = param4;
         this.userList = [];
         this.variables = [];
      }
      
      public function addUser(param1:User, param2:int) : void
      {
         this.userList[param2] = param1;
         ++this.userCount;
      }
      
      public function removeUser(param1:int) : void
      {
         var _loc2_:User = this.userList[param1];
         --this.userCount;
         delete this.userList[param1];
      }
      
      public function getUserList() : Array
      {
         return this.userList;
      }
      
      public function getUser(param1:*) : User
      {
         var _loc3_:String = null;
         var _loc4_:User = null;
         var _loc2_:User = null;
         if(typeof param1 == "number")
         {
            _loc2_ = this.userList[param1];
         }
         else if(typeof param1 == "string")
         {
            for(_loc3_ in this.userList)
            {
               _loc4_ = this.userList[_loc3_];
               if(_loc4_.getName() == param1)
               {
                  _loc2_ = _loc4_;
                  break;
               }
            }
         }
         return _loc2_;
      }
      
      public function clearUserList() : void
      {
         this.userList = [];
         this.userCount = 0;
      }
      
      public function getVariable(param1:String) : *
      {
         return this.variables[param1];
      }
      
      public function getVariables() : Array
      {
         return this.variables;
      }
      
      public function setVariables(param1:Array) : void
      {
         this.variables = param1;
      }
      
      public function clearVariables() : void
      {
         this.variables = [];
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getId() : int
      {
         return this.id;
      }
      
      public function getUserCount() : int
      {
         return this.userCount;
      }
      
      public function getMaxUsers() : int
      {
         return this.maxUsers;
      }
      
      public function setUserCount(param1:int) : void
      {
         this.userCount = param1;
      }
   }
}

