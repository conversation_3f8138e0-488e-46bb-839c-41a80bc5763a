package mmo.socketserver.handlers
{
   import flash.utils.ByteArray;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   
   public class ExtBHandler implements IMessageHandler
   {
      private var ssc:SocketServerClient;
      
      public function ExtBHandler(param1:SocketServerClient)
      {
         super();
         this.ssc = param1;
      }
      
      public function handleMessage(param1:Object) : void
      {
         var _loc2_:ByteArray = param1 as ByteArray;
         var _loc3_:Object = {"dataObj":_loc2_};
         var _loc4_:SSEvent = new SSEvent(SSEvent.onExtensionResponseByte,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
   }
}

