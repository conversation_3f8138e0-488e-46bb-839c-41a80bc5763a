package mmo.socketserver.handlers
{
   import flash.utils.ByteArray;
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.protocol.ExMsgCodec;
   import mmo.socketserver.util.ObjectSerializer;
   
   public class Ext<PERSON>andler implements IMessageHandler
   {
      private var ssc:SocketServerClient;
      
      public function ExtHandler(param1:SocketServerClient)
      {
         super();
         this.ssc = param1;
      }
      
      public function handleMessage(param1:Object) : void
      {
         var _loc2_:Object = null;
         var _loc3_:SSEvent = null;
         var _loc6_:String = null;
         var _loc4_:ByteArray = param1 as ByteArray;
         var _loc5_:Object = ExMsgCodec.readObject(_loc4_);
         if(this.ssc.debug)
         {
            _loc6_ = ObjectSerializer.getInstance(true).serialize(_loc5_);
            this.ssc.debugMessage("[extension msg data]:" + _loc6_);
         }
         _loc2_ = {};
         _loc2_.dataObj = _loc5_;
         _loc3_ = new SSEvent(SSEvent.onExtensionResponse,_loc2_);
         this.ssc.dispatchEvent(_loc3_);
      }
   }
}

