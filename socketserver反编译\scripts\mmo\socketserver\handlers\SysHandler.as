package mmo.socketserver.handlers
{
   import mmo.socketserver.SSEvent;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.data.Room;
   import mmo.socketserver.data.User;
   import mmo.socketserver.protocol.*;
   import mmo.socketserver.util.Entities;
   import mmo.socketserver.util.ObjectSerializer;
   
   public class <PERSON>ys<PERSON>andler implements IMessageHandler
   {
      private var ssc:SocketServerClient;
      
      private var handlersTable:Object;
      
      private var msgReader:IMsgReader;
      
      public function SysHandler(param1:SocketServerClient)
      {
         super();
         this.ssc = param1;
         this.setMsgReader();
         this.handlersTable = {};
         this.handlersTable[DownCmd.logKO] = this.handleLoginKo;
         this.handlersTable[DownCmd.joinOK] = this.handleJoinOk;
         this.handlersTable[DownCmd.joinKO] = this.handleJoinKo;
         this.handlersTable[DownCmd.uER] = this.handleUserEnterRoom;
         this.handlersTable[DownCmd.userGone] = this.handleUserLeaveRoom;
         this.handlersTable[DownCmd.pubMsg] = this.handlePublicMessage;
         this.handlersTable[DownCmd.dmnMsg] = this.handleAdminMessage;
         this.handlersTable[DownCmd.dataObj] = this.handleASObject;
         this.handlersTable[DownCmd.rVarsUpdate] = this.handleRoomVarsUpdate;
         this.handlersTable[DownCmd.uVarsUpdate] = this.handleUserVarsUpdate;
         this.handlersTable[DownCmd.createRmKO] = this.handleCreateRoomError;
         this.handlersTable[DownCmd.leaveRoom] = this.handleLeaveRoom;
      }
      
      private function setMsgReader() : void
      {
         this.msgReader = new BinaryMsgReader();
         this.msgReader.setServer(this.ssc);
      }
      
      public function handleMessage(param1:Object) : void
      {
         var _loc2_:String = this.msgReader.handleMessage(param1).action;
         if(this.ssc.debug)
         {
            this.ssc.debugMessage("[Receive msg action]:" + _loc2_);
         }
         var _loc3_:Function = this.handlersTable[_loc2_];
         if(_loc3_ != null)
         {
            _loc3_.apply(this,[param1]);
         }
      }
      
      public function handleLoginKo(param1:Object) : void
      {
         var _loc2_:Object = {};
         _loc2_.success = false;
         _loc2_.error = this.msgReader.handleLoginKo(param1).error;
         var _loc3_:SSEvent = new SSEvent(SSEvent.onLogin,_loc2_);
         this.ssc.dispatchEvent(_loc3_);
      }
      
      public function handleJoinOk(param1:Object) : void
      {
         var _loc2_:Room = this.msgReader.handleJoinOk(param1).currRoom;
         this.ssc.changingRoom = false;
         var _loc3_:Object = {};
         _loc3_.room = _loc2_;
         var _loc4_:SSEvent = new SSEvent(SSEvent.onJoinRoom,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      public function handleJoinKo(param1:Object) : void
      {
         this.ssc.changingRoom = false;
         var _loc2_:Object = {};
         _loc2_.error = this.msgReader.handleJoinKo(param1).error;
         var _loc3_:SSEvent = new SSEvent(SSEvent.onJoinRoomError,_loc2_);
         this.ssc.dispatchEvent(_loc3_);
      }
      
      public function handleUserEnterRoom(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleUserEnterRoom(param1);
         var _loc3_:Object = {};
         _loc3_.roomId = _loc2_.roomId;
         _loc3_.user = _loc2_.newUser;
         _loc3_.isInit = _loc2_.isInit;
         var _loc4_:SSEvent = new SSEvent(SSEvent.onUserEnterRoom,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      public function handleUserLeaveRoom(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleUserLeaveRoom(param1);
         var _loc3_:int = int(_loc2_.userId);
         var _loc4_:int = int(_loc2_.roomId);
         if(_loc4_ != this.ssc.activeRoomId)
         {
            return;
         }
         var _loc5_:User = this.ssc.activeRoom.getUser(_loc3_);
         if(_loc5_ == null)
         {
            return;
         }
         var _loc6_:String = _loc5_.getName();
         this.ssc.activeRoom.removeUser(_loc3_);
         var _loc7_:Object = {};
         _loc7_.roomId = _loc4_;
         _loc7_.userId = _loc3_;
         _loc7_.userName = _loc6_;
         var _loc8_:SSEvent = new SSEvent(SSEvent.onUserLeaveRoom,_loc7_);
         this.ssc.dispatchEvent(_loc8_);
      }
      
      public function handlePublicMessage(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handlePublicMessage(param1);
         var _loc3_:int = int(_loc2_.roomId);
         var _loc4_:int = int(_loc2_.userId);
         var _loc5_:String = _loc2_.message;
         if(_loc3_ != this.ssc.activeRoomId)
         {
            return;
         }
         var _loc6_:User = this.ssc.activeRoom.getUser(_loc4_);
         var _loc7_:Object = {};
         _loc7_.message = Entities.decodeEntities(_loc5_);
         _loc7_.sender = _loc6_;
         _loc7_.roomId = _loc3_;
         var _loc8_:SSEvent = new SSEvent(SSEvent.onPublicMessage,_loc7_);
         this.ssc.dispatchEvent(_loc8_);
      }
      
      public function handleAdminMessage(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleAdminMessage(param1);
         var _loc3_:int = int(_loc2_.roomId);
         var _loc4_:int = int(_loc2_.userId);
         var _loc5_:String = _loc2_.message;
         var _loc6_:Object = {};
         _loc6_.message = Entities.decodeEntities(_loc5_);
         var _loc7_:SSEvent = new SSEvent(SSEvent.onAdminMessage,_loc6_);
         this.ssc.dispatchEvent(_loc7_);
      }
      
      public function handleASObject(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleASObject(param1);
         var _loc3_:int = int(_loc2_.roomId);
         var _loc4_:int = int(_loc2_.userId);
         var _loc5_:String = _loc2_.dataObj;
         if(_loc3_ != this.ssc.activeRoomId)
         {
            return;
         }
         var _loc6_:User = this.ssc.activeRoom.getUser(_loc4_);
         var _loc7_:Object = ObjectSerializer.getInstance().deserialize(new XML(_loc5_));
         var _loc8_:Object = {};
         _loc8_.obj = _loc7_;
         _loc8_.sender = _loc6_;
         var _loc9_:SSEvent = new SSEvent(SSEvent.onObjectReceived,_loc8_);
         this.ssc.dispatchEvent(_loc9_);
      }
      
      public function handleRoomVarsUpdate(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleRoomVarsUpdate(param1);
         var _loc3_:Object = {};
         _loc3_.room = _loc2_.currRoom;
         _loc3_.changedVars = _loc2_.changedVars;
         if(_loc2_.currRoom == null)
         {
            return;
         }
         var _loc4_:SSEvent = new SSEvent(SSEvent.onRoomVariablesUpdate,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      public function handleUserVarsUpdate(param1:Object) : void
      {
         var _loc2_:Object = this.msgReader.handleUserVarsUpdate(param1);
         if(_loc2_ == null)
         {
            return;
         }
         var _loc3_:Object = {};
         _loc3_.user = _loc2_.currUser;
         _loc3_.changedVars = _loc2_.changedVars;
         var _loc4_:SSEvent = new SSEvent(SSEvent.onUserVariablesUpdate,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      private function handleCreateRoomError(param1:Object) : void
      {
         var _loc2_:String = this.msgReader.handleCreateRoomError(param1).errMsg;
         var _loc3_:Object = {};
         _loc3_.error = _loc2_;
         var _loc4_:SSEvent = new SSEvent(SSEvent.onCreateRoomError,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      private function handleLeaveRoom(param1:Object) : void
      {
         var _loc2_:int = int(this.msgReader.handleLeaveRoom(param1).roomLeft);
         var _loc3_:Object = {};
         _loc3_.roomId = _loc2_;
         var _loc4_:SSEvent = new SSEvent(SSEvent.onRoomLeft,_loc3_);
         this.ssc.dispatchEvent(_loc4_);
      }
      
      public function dispatchDisconnection() : void
      {
         var _loc1_:SSEvent = new SSEvent(SSEvent.onConnectionLost,null);
         this.ssc.dispatchEvent(_loc1_);
      }
   }
}

