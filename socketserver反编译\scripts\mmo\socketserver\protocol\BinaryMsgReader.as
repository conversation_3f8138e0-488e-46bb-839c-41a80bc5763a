package mmo.socketserver.protocol
{
   import flash.utils.ByteArray;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.data.Room;
   import mmo.socketserver.data.User;
   
   public class BinaryMsgReader implements IMsgReader
   {
      private var server:SocketServerClient;
      
      private var roomId:int;
      
      private var byteData:ByteArray;
      
      public function BinaryMsgReader()
      {
         super();
      }
      
      public function setServer(param1:SocketServerClient) : *
      {
         this.server = param1;
      }
      
      public function handleMessage(param1:Object) : Object
      {
         this.byteData = param1 as ByteArray;
         var _loc2_:int = this.readShort();
         this.roomId = this.readInt();
         return {"action":_loc2_};
      }
      
      public function handleLoginKo(param1:Object) : Object
      {
         return {"error":this.readPrefixString()};
      }
      
      public function handleJoinOk(param1:Object) : Object
      {
         var currRoom:Room;
         var varCount:int;
         var uCount:int;
         var i:*;
         var id:int = 0;
         var name:String = null;
         var user:User = null;
         var uVarCount:int = 0;
         var o:Object = param1;
         var roomId:int = this.readInt();
         var roomName:String = this.readPrefixString();
         var maxUsers:int = this.readInt();
         var joinData:String = this.readPrefixString();
         this.server.activeRoomId = roomId;
         this.server.activeRoom = new Room(roomId,roomName,maxUsers);
         currRoom = this.server.activeRoom;
         currRoom.clearUserList();
         varCount = this.readShort();
         if(varCount > 0)
         {
            currRoom.clearVariables();
            this.populateVariables(currRoom.getVariables(),varCount);
         }
         uCount = this.readShort();
         i = 0;
         while(i < uCount)
         {
            try
            {
               id = this.readInt();
            }
            catch(e:Error)
            {
               break;
            }
            name = this.readPrefixString();
            user = new User(id,name);
            uVarCount = this.readShort();
            if(uVarCount > 0)
            {
               this.populateVariables(user.getVariables(),uVarCount);
               user.setCustomPropertyByVars();
            }
            currRoom.addUser(user,id);
            i++;
         }
         return {
            "currRoom":currRoom,
            "joinData":joinData
         };
      }
      
      public function handleJoinKo(param1:Object) : Object
      {
         return {"error":this.readPrefixString()};
      }
      
      public function handleUserEnterRoom(param1:Object) : Object
      {
         var _loc2_:int = this.roomId;
         var _loc3_:int = this.readInt();
         var _loc4_:String = this.readPrefixString();
         var _loc5_:* = this.readBoolean();
         var _loc6_:String = this.readPrefixString();
         var _loc7_:User = new User(_loc3_,_loc4_);
         var _loc8_:int = this.readShort();
         if(_loc8_ > 0)
         {
            this.populateVariables(_loc7_.getVariables(),_loc8_);
            _loc7_.setCustomPropertyByVars();
         }
         if(this.server.activeRoomId == _loc2_)
         {
            this.server.activeRoom.addUser(_loc7_,_loc3_);
         }
         return {
            "roomId":_loc2_,
            "newUser":_loc7_,
            "isInit":_loc5_,
            "joinData":_loc6_
         };
      }
      
      public function handleUserLeaveRoom(param1:Object) : Object
      {
         return {
            "userId":this.readInt(),
            "roomId":this.roomId
         };
      }
      
      public function handlePublicMessage(param1:Object) : Object
      {
         return {
            "roomId":this.roomId,
            "userId":this.readInt(),
            "message":this.readPrefixString()
         };
      }
      
      public function handleAdminMessage(param1:Object) : Object
      {
         return {
            "roomId":this.roomId,
            "userId":this.readInt(),
            "message":this.readPrefixString()
         };
      }
      
      public function handleASObject(param1:Object) : Object
      {
         return {
            "roomId":this.roomId,
            "userId":this.readInt(),
            "dataObj":this.readPrefixString()
         };
      }
      
      public function handleRoomVarsUpdate(param1:Object) : Object
      {
         var _loc3_:Array = null;
         var _loc4_:Room = null;
         var _loc2_:int = this.roomId;
         if(_loc2_ == this.server.activeRoomId)
         {
            _loc3_ = this.server.activeRoom.getVariables();
            _loc4_ = this.server.activeRoom;
         }
         else
         {
            _loc3_ = [];
            _loc4_ = null;
         }
         var _loc5_:Array = [];
         var _loc6_:int = this.readShort();
         if(_loc6_ > 0)
         {
            this.populateVariables(_loc3_,_loc6_,_loc5_);
         }
         return {
            "currRoom":_loc4_,
            "changedVars":_loc5_
         };
      }
      
      public function handleUserVarsUpdate(param1:Object) : Object
      {
         var _loc5_:Array = null;
         var _loc2_:int = this.roomId;
         var _loc3_:int = this.readInt();
         var _loc4_:User = null;
         if(_loc2_ == this.server.activeRoomId)
         {
            _loc4_ = this.server.activeRoom.getUser(_loc3_);
         }
         _loc5_ = _loc4_ == null ? [] : _loc4_.getVariables();
         var _loc6_:Array = [];
         var _loc7_:int = this.readShort();
         if(_loc7_ > 0)
         {
            this.populateVariables(_loc5_,_loc7_,_loc6_);
         }
         return _loc4_ != null ? {
            "currUser":_loc4_,
            "changedVars":_loc6_
         } : null;
      }
      
      public function handleCreateRoomError(param1:Object) : Object
      {
         return {"errMsg":this.readPrefixString()};
      }
      
      public function handleLeaveRoom(param1:Object) : Object
      {
         return {"roomLeft":this.readInt()};
      }
      
      public function populateVariables(param1:Array, param2:int, param3:Array = null) : void
      {
         var _loc5_:String = null;
         var _loc6_:String = null;
         var _loc7_:String = null;
         var _loc4_:* = 0;
         while(_loc4_ < param2)
         {
            _loc5_ = this.readPrefixString();
            _loc6_ = this.readChar();
            _loc7_ = this.readPrefixString();
            if(param3 != null)
            {
               param3.push(_loc5_);
               param3[_loc5_] = true;
            }
            if(_loc6_ == "b")
            {
               param1[_loc5_] = _loc7_ == "1" ? true : false;
            }
            else if(_loc6_ == "n")
            {
               param1[_loc5_] = Number(_loc7_);
            }
            else if(_loc6_ == "s")
            {
               param1[_loc5_] = _loc7_;
            }
            else if(_loc6_ == "x")
            {
               delete param1[_loc5_];
            }
            _loc4_++;
         }
      }
      
      private function readAndSetKeyStringValue(param1:Object) : void
      {
         var _loc2_:String = this.readPrefixString();
         var _loc3_:String = this.readPrefixString();
         param1[_loc2_] = _loc3_;
      }
      
      private function readPrefixString() : String
      {
         return this.byteData.readUTF();
      }
      
      private function readBoolean() : Boolean
      {
         return this.byteData.readByte() == 49;
      }
      
      private function readShort() : int
      {
         return this.byteData.readShort();
      }
      
      private function readInt() : int
      {
         return this.byteData.readInt();
      }
      
      private function readChar() : String
      {
         return String.fromCharCode(this.byteData.readByte());
      }
   }
}

