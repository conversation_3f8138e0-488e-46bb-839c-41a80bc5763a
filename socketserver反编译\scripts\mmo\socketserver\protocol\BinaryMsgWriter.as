package mmo.socketserver.protocol
{
   import flash.utils.ByteArray;
   import mmo.socketserver.SocketServerClient;
   import mmo.socketserver.util.Entities;
   import mmo.socketserver.util.ObjectSerializer;
   
   public class BinaryMsgWriter implements IMsgWriter
   {
      private var server:SocketServerClient;
      
      private var byteData:ByteArray = null;
      
      public function BinaryMsgWriter()
      {
         super();
      }
      
      public function setServer(param1:SocketServerClient) : *
      {
         this.server = param1;
      }
      
      public function joinRoom(param1:String, param2:Boolean = false, param3:Boolean = false, param4:int = -1, param5:String = "") : void
      {
         var _loc6_:Boolean = param3 ? false : true;
         var _loc7_:int = this.server.activeRoomId;
         var _loc8_:int = param4 > -1 ? param4 : _loc7_;
         if(_loc7_ == -1)
         {
            _loc6_ = false;
            _loc8_ = -1;
         }
         this.resetBuffer(UpCmd.JoinRoom,_loc7_);
         this.writePrefixString(param1);
         this.writeBoolean(!param3);
         this.writeInt(_loc8_);
         this.writeBoolean(param2);
         this.writePrefixString(param5);
         this.send();
      }
      
      public function leaveRoom(param1:int) : void
      {
         this.resetBuffer(UpCmd.LeaveRoom);
         this.writeInt(param1);
         this.send();
      }
      
      public function login(param1:String, param2:String, param3:String) : void
      {
         this.resetBuffer(UpCmd.Login);
         this.writePrefixString(param1);
         this.writePrefixString(param2);
         this.writePrefixString(param3);
         this.send();
      }
      
      public function hit() : void
      {
         this.resetBuffer(UpCmd.Hit,this.server.activeRoomId);
         this.send();
      }
      
      public function sendPublicMessage(param1:String, param2:int) : void
      {
         this.resetBuffer(UpCmd.PubMsg,param2);
         this.writePrefixString(Entities.encodeEntities(param1));
         this.send();
      }
      
      public function sendObject(param1:Object, param2:int) : void
      {
         this.resetBuffer(UpCmd.AsObj,param2);
         this.writePrefixString(ObjectSerializer.getInstance().serialize(param1));
         this.send();
      }
      
      public function sendXtMessage(param1:int, param2:Object) : void
      {
         this.resetMsgBuffer(1,UpCmd.XtReq,param1);
         this.writeShort(param2.id);
         this.writePrefixString(param2.cmd);
         ExMsgCodec.writeObject(param2.param,this.byteData);
         this.send();
      }
      
      public function sendXtMessageByte(param1:int, param2:ByteArray) : void
      {
         this.resetMsgBuffer(1,UpCmd.XtReqB,param1);
         this.byteData.writeBytes(param2);
         this.send();
      }
      
      public function setRoomVariables(param1:Array, param2:int, param3:Boolean = true) : void
      {
         var _loc4_:Object = null;
         this.resetBuffer(UpCmd.SetRvars,param2);
         this.writeBoolean(param3);
         this.writeShort(param1.length);
         for each(_loc4_ in param1)
         {
            this.setBinaryRoomVariable(_loc4_);
         }
         this.send();
      }
      
      public function setUserVariables(param1:Object, param2:int) : void
      {
         this.resetBuffer(UpCmd.SetUvars,param2);
         this.setBinaryUserVariable(param1);
         this.send();
      }
      
      private function send() : void
      {
         this.server.writeBinaryToSocket(this.byteData);
      }
      
      private function resetBuffer(param1:int, param2:int = -1) : void
      {
         this.resetMsgBuffer(0,param1,param2);
      }
      
      private function resetMsgBuffer(param1:int, param2:int, param3:int = -1) : void
      {
         this.byteData = new ByteArray();
         this.byteData.writeByte(param1);
         this.writeShort(param2);
         this.writeInt(param3);
         if(this.server.debug)
         {
            this.server.debugMessage("[Start send msg action:]" + param2);
         }
      }
      
      private function setBinaryUserVariable(param1:Object) : void
      {
         var _loc2_:* = undefined;
         var _loc3_:String = null;
         var _loc4_:* = null;
         var _loc7_:String = null;
         var _loc8_:Object = null;
         var _loc9_:* = undefined;
         var _loc5_:int = 0;
         var _loc6_:Array = new Array();
         for(_loc7_ in param1)
         {
            _loc2_ = param1[_loc7_];
            _loc4_ = typeof _loc2_;
            _loc3_ = null;
            if(_loc4_ == "boolean")
            {
               _loc3_ = "b";
               _loc2_ = !!_loc2_ ? "1" : "0";
            }
            else if(_loc4_ == "number")
            {
               _loc3_ = "n";
            }
            else if(_loc4_ == "string")
            {
               _loc3_ = "s";
            }
            else if(_loc2_ == null && _loc4_ == "object" || _loc4_ == "undefined")
            {
               _loc3_ = "x";
               _loc2_ = "";
            }
            if(_loc3_ != null)
            {
               _loc5_ += 1;
               _loc6_.push({
                  "n":_loc7_,
                  "t":_loc3_,
                  "v":_loc2_
               });
            }
         }
         this.writeShort(_loc5_);
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc8_ = _loc6_[_loc9_];
            this.writePrefixString(_loc8_.n);
            this.writeChar(_loc8_.t);
            this.writePrefixString(_loc8_.v);
            _loc9_++;
         }
      }
      
      private function setBinaryRoomVariable(param1:Object) : void
      {
         var _loc2_:String = param1.name.toString();
         var _loc3_:* = param1.val;
         var _loc4_:Boolean = !!param1.priv ? true : false;
         var _loc5_:Boolean = !!param1.persistent ? true : false;
         var _loc6_:String = null;
         var _loc7_:* = typeof _loc3_;
         if(_loc7_ == "boolean")
         {
            _loc6_ = "b";
            _loc3_ = !!_loc3_ ? "1" : "0";
         }
         else if(_loc7_ == "number")
         {
            _loc6_ = "n";
         }
         else if(_loc7_ == "string")
         {
            _loc6_ = "s";
         }
         else if(_loc3_ == null && _loc7_ == "object" || _loc7_ == "undefined")
         {
            _loc6_ = "x";
            _loc3_ = "";
         }
         if(_loc6_ != null)
         {
            this.writePrefixString(_loc2_);
            this.writeChar(_loc6_);
            this.writePrefixString(_loc3_);
            this.writeBoolean(_loc5_);
            this.writeBoolean(_loc4_);
            return;
         }
         throw new Error("Invalid room var type.");
      }
      
      private function writePrefixString(param1:String) : void
      {
         this.byteData.writeUTF(param1);
      }
      
      private function writeBoolean(param1:Boolean) : void
      {
         this.byteData.writeByte(param1 ? 49 : 48);
      }
      
      private function writeShort(param1:int) : void
      {
         this.byteData.writeShort(param1);
      }
      
      private function writeInt(param1:int) : void
      {
         this.byteData.writeInt(param1);
      }
      
      private function writeChar(param1:String) : void
      {
         this.byteData.writeByte(int(param1.charCodeAt(0)));
      }
      
      private function writeObject(param1:Object) : void
      {
         this.byteData.writeObject(param1);
      }
   }
}

