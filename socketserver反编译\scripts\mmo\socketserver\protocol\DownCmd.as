package mmo.socketserver.protocol
{
   public class DownCmd
   {
      public static const createRmKO:int = 20015;
      
      public static const dataObj:int = 20016;
      
      public static const dmnMsg:int = 20017;
      
      public static const joinKO:int = 20018;
      
      public static const joinOK:int = 20019;
      
      public static const leaveRoom:int = 20020;
      
      public static const logKO:int = 20021;
      
      public static const pubMsg:int = 20026;
      
      public static const rVarsUpdate:int = 20034;
      
      public static const uER:int = 20036;
      
      public static const userGone:int = 20037;
      
      public static const uVarsUpdate:int = 20039;
      
      public static const xtRes:int = 20041;
      
      public static const crossDomain:int = 20042;
      
      public static const xtResB:int = 20043;
      
      public function DownCmd()
      {
         super();
      }
   }
}

