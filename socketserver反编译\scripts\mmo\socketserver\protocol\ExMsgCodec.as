package mmo.socketserver.protocol
{
   import flash.utils.ByteArray;
   
   public class ExMsgCodec
   {
      private static var isXml:Boolean = false;
      
      public function ExMsgCodec()
      {
         super();
      }
      
      public static function initExMsgCodec() : void
      {
      }
      
      public static function readObject(param1:ByteArray) : Object
      {
         return param1.readObject();
      }
      
      public static function writeObject(param1:Object, param2:ByteArray) : void
      {
         param2.writeObject(param1);
      }
   }
}

