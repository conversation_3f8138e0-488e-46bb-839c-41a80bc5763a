package mmo.socketserver.protocol
{
   import mmo.socketserver.SocketServerClient;
   
   public interface IMsgReader
   {
      function setServer(param1:SocketServerClient) : *;
      
      function handleMessage(param1:Object) : Object;
      
      function handleLoginKo(param1:Object) : Object;
      
      function handleJoinOk(param1:Object) : Object;
      
      function handleJoinKo(param1:Object) : Object;
      
      function handleUserEnterRoom(param1:Object) : Object;
      
      function handleUserLeaveRoom(param1:Object) : Object;
      
      function handlePublicMessage(param1:Object) : Object;
      
      function handleAdminMessage(param1:Object) : Object;
      
      function handleASObject(param1:Object) : Object;
      
      function handleRoomVarsUpdate(param1:Object) : Object;
      
      function handleUserVarsUpdate(param1:Object) : Object;
      
      function handleCreateRoomError(param1:Object) : Object;
      
      function handleLeaveRoom(param1:Object) : Object;
   }
}

