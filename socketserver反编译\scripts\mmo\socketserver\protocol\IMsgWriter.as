package mmo.socketserver.protocol
{
   import flash.utils.ByteArray;
   import mmo.socketserver.SocketServerClient;
   
   public interface IMsgWriter
   {
      function setServer(param1:SocketServerClient) : *;
      
      function joinRoom(param1:String, param2:Boolean = false, param3:<PERSON>olean = false, param4:int = -1, param5:String = "") : void;
      
      function leaveRoom(param1:int) : void;
      
      function login(param1:String, param2:String, param3:String) : void;
      
      function hit() : void;
      
      function sendPublicMessage(param1:String, param2:int) : void;
      
      function sendObject(param1:Object, param2:int) : void;
      
      function sendXtMessage(param1:int, param2:Object) : void;
      
      function sendXtMessageByte(param1:int, param2:ByteArray) : void;
      
      function setRoomVariables(param1:Array, param2:int, param3:Boolean = true) : void;
      
      function setUserVariables(param1:Object, param2:int) : void;
   }
}

