package mmo.socketserver.protocol
{
   public class UpCmd
   {
      public static const AsObj:int = 10002;
      
      public static const Hit:int = 10011;
      
      public static const JoinRoom:int = 10012;
      
      public static const LeaveRoom:int = 10013;
      
      public static const Login:int = 10016;
      
      public static const PubMsg:int = 10020;
      
      public static const SetRvars:int = 10027;
      
      public static const SetUvars:int = 10028;
      
      public static const XtReq:int = 10033;
      
      public static const XtReqB:int = 10034;
      
      public function UpCmd()
      {
         super();
      }
   }
}

