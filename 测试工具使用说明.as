/**
 * 龙斗士游戏发包抓包测试工具使用说明
 * 
 * 基于现有的ActionScript代码修改，无需创建新类
 * 所有功能都集成在SocketServerClient和ServerClient中
 */

package
{
   import mmo.ServerClient;
   import mmo.socketserver.SocketServerClient;
   
   public class TestToolUsageExample
   {
      private var serverClient:ServerClient;
      
      public function TestToolUsageExample()
      {
         // 初始化
         this.serverClient = ServerClient.instance;
         this.setupTestEnvironment();
      }
      
      /**
       * 设置测试环境
       */
      private function setupTestEnvironment():void
      {
         // 1. 启用数据包日志记录
         this.serverClient.enablePacketLogging();
         
         // 2. 设置数据包拦截器
         this.serverClient.setInterceptor(this.packetInterceptor);
         
         trace("测试环境已设置完成");
      }
      
      /**
       * 数据包拦截器示例
       */
      private function packetInterceptor(packet:*, direction:String):Object
      {
         trace("[拦截器] " + direction + " 数据包被拦截");
         
         // 可以选择阻止数据包
         if(direction == "outgoing" && Math.random() < 0.1)
         {
            return {block: true, modified: false, data: packet};
         }
         
         // 可以选择修改数据包
         // return {block: false, modified: true, data: modifiedPacket};
         
         // 正常通过
         return {block: false, modified: false, data: packet};
      }
      
      /**
       * 基础功能测试
       */
      public function basicFunctionTest():void
      {
         trace("=== 基础功能测试 ===");
         
         // 连接到服务器
         this.serverClient.login("127.0.0.1", 9339, "testZone", "testUser", "testPass");
         
         // 等待连接成功后执行其他测试
         setTimeout(this.runAdvancedTests, 2000);
      }
      
      /**
       * 高级测试功能
       */
      private function runAdvancedTests():void
      {
         trace("=== 高级测试功能 ===");
         
         // 1. 发送自定义数据包
         this.testCustomPackets();
         
         // 2. 漏洞测试
         this.testVulnerabilities();
         
         // 3. 压力测试
         this.testStress();
         
         // 4. 模糊测试
         this.testFuzzing();
         
         // 5. 数据包重放
         this.testReplay();
      }
      
      /**
       * 自定义数据包测试
       */
      private function testCustomPackets():void
      {
         trace("--- 自定义数据包测试 ---");
         
         // 发送十六进制数据包
         this.serverClient.sendCustomPacket("00 01 02 03 04 05");
         
         // 发送登录数据包
         this.serverClient.sendCustomPacket("00 27 30 FF FF FF FF");
         
         trace("自定义数据包已发送");
      }
      
      /**
       * 漏洞测试
       */
      private function testVulnerabilities():void
      {
         trace("--- 漏洞测试 ---");
         
         // 缓冲区溢出测试
         this.serverClient.enableVulnTesting("overflow");
         this.serverClient.socketServerClient.hit(); // 发送心跳包进行测试
         
         setTimeout(function():void {
            // SQL注入测试
            this.serverClient.enableVulnTesting("injection");
            this.serverClient.socketServerClient.sendPublicMessage("test message");
         }, 1000);
         
         setTimeout(function():void {
            // 畸形数据包测试
            this.serverClient.enableVulnTesting("malformed");
            this.serverClient.socketServerClient.hit();
         }, 2000);
         
         setTimeout(function():void {
            // 重放攻击测试
            this.serverClient.enableVulnTesting("replay");
            this.serverClient.socketServerClient.hit();
            
            // 禁用漏洞测试
            this.serverClient.disableVulnTesting();
         }, 3000);
         
         trace("漏洞测试序列已启动");
      }
      
      /**
       * 压力测试
       */
      private function testStress():void
      {
         trace("--- 压力测试 ---");
         
         // 发送1000个数据包进行压力测试
         this.serverClient.stressTest(1000);
         
         trace("压力测试已启动");
      }
      
      /**
       * 模糊测试
       */
      private function testFuzzing():void
      {
         trace("--- 模糊测试 ---");
         
         // 发送10个随机数据包
         for(var i:int = 0; i < 10; i++)
         {
            setTimeout(function():void {
               this.serverClient.fuzzTest();
            }, i * 500);
         }
         
         trace("模糊测试已启动");
      }
      
      /**
       * 数据包重放测试
       */
      private function testReplay():void
      {
         trace("--- 数据包重放测试 ---");
         
         // 等待一些数据包被记录后进行重放
         setTimeout(function():void {
            var packetLog:Array = this.serverClient.getPacketLog();
            if(packetLog.length > 0)
            {
               // 重放第一个发送的数据包
               for(var i:int = 0; i < packetLog.length; i++)
               {
                  if(packetLog[i].direction == "outgoing")
                  {
                     this.serverClient.replayPacket(i);
                     break;
                  }
               }
            }
         }, 5000);
         
         trace("数据包重放测试已设置");
      }
      
      /**
       * 批量测试
       */
      public function batchTest():void
      {
         trace("--- 批量测试 ---");
         
         // 每100ms发送一个数据包，总共发送50个
         this.serverClient.batchTest(50, 100);
         
         trace("批量测试已启动");
      }
      
      /**
       * 获取测试结果
       */
      public function getTestResults():void
      {
         trace("=== 测试结果 ===");
         
         // 获取连接统计
         var stats:Object = this.serverClient.getStats();
         trace("连接状态: " + stats.isConnected);
         trace("用户ID: " + stats.myUserId);
         trace("用户名: " + stats.myUserName);
         trace("发送数据包数: " + stats.packetsSent);
         trace("接收数据包数: " + stats.packetsReceived);
         trace("是否空闲: " + stats.isIdle);
         
         // 导出数据包日志
         var logData:String = this.serverClient.exportPacketLog();
         trace("数据包日志:\n" + logData);
         
         // 清空日志
         this.serverClient.clearPacketLog();
         trace("日志已清空");
      }
      
      /**
       * 清理测试环境
       */
      public function cleanup():void
      {
         trace("=== 清理测试环境 ===");
         
         // 移除拦截器
         this.serverClient.removeInterceptor();
         
         // 禁用日志记录
         this.serverClient.disablePacketLogging();
         
         // 断开连接
         this.serverClient.disconnect();
         
         trace("测试环境已清理");
      }
   }
}

/**
 * 使用方法:
 * 
 * 1. 基础使用:
 *    var tester = new TestToolUsageExample();
 *    tester.basicFunctionTest();
 * 
 * 2. 单独功能测试:
 *    tester.batchTest();        // 批量测试
 *    tester.getTestResults();   // 查看结果
 * 
 * 3. 清理:
 *    tester.cleanup();
 * 
 * 4. 直接使用ServerClient:
 *    ServerClient.instance.enablePacketLogging();
 *    ServerClient.instance.sendCustomPacket("FF FF FF FF");
 *    ServerClient.instance.enableVulnTesting("overflow");
 */
