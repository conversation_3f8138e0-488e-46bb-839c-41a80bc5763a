/**
 * 龙斗士游戏测试工具控制台
 * 提供简单的命令行界面来控制测试功能
 */

package
{
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import mmo.ServerClient;
   
   public class TestToolConsole extends Sprite
   {
      private var serverClient:ServerClient;
      private var outputField:TextField;
      private var inputField:TextField;
      private var commandHistory:Array = [];
      private var historyIndex:int = -1;
      
      public function TestToolConsole()
      {
         super();
         this.serverClient = ServerClient.instance;
         this.setupUI();
         this.setupCommands();
         this.printWelcome();
      }
      
      /**
       * 设置用户界面
       */
      private function setupUI():void
      {
         // 输出区域
         this.outputField = new TextField();
         this.outputField.width = 800;
         this.outputField.height = 500;
         this.outputField.x = 10;
         this.outputField.y = 10;
         this.outputField.border = true;
         this.outputField.backgroundColor = 0x000000;
         this.outputField.textColor = 0x00FF00;
         this.outputField.multiline = true;
         this.outputField.wordWrap = true;
         
         var outputFormat:TextFormat = new TextFormat();
         outputFormat.font = "Courier New";
         outputFormat.size = 12;
         this.outputField.defaultTextFormat = outputFormat;
         
         addChild(this.outputField);
         
         // 输入区域
         this.inputField = new TextField();
         this.inputField.width = 800;
         this.inputField.height = 25;
         this.inputField.x = 10;
         this.inputField.y = 520;
         this.inputField.border = true;
         this.inputField.backgroundColor = 0x333333;
         this.inputField.textColor = 0xFFFFFF;
         this.inputField.type = "input";
         
         var inputFormat:TextFormat = new TextFormat();
         inputFormat.font = "Courier New";
         inputFormat.size = 12;
         this.inputField.defaultTextFormat = inputFormat;
         
         addChild(this.inputField);
         
         // 事件监听
         this.inputField.addEventListener(KeyboardEvent.KEY_DOWN, this.onKeyDown);
         stage.focus = this.inputField;
      }
      
      /**
       * 键盘事件处理
       */
      private function onKeyDown(event:KeyboardEvent):void
      {
         if(event.keyCode == Keyboard.ENTER)
         {
            var command:String = this.inputField.text.trim();
            if(command.length > 0)
            {
               this.executeCommand(command);
               this.commandHistory.push(command);
               this.historyIndex = this.commandHistory.length;
               this.inputField.text = "";
            }
         }
         else if(event.keyCode == Keyboard.UP)
         {
            if(this.historyIndex > 0)
            {
               this.historyIndex--;
               this.inputField.text = this.commandHistory[this.historyIndex];
            }
         }
         else if(event.keyCode == Keyboard.DOWN)
         {
            if(this.historyIndex < this.commandHistory.length - 1)
            {
               this.historyIndex++;
               this.inputField.text = this.commandHistory[this.historyIndex];
            }
            else
            {
               this.historyIndex = this.commandHistory.length;
               this.inputField.text = "";
            }
         }
      }
      
      /**
       * 设置命令系统
       */
      private function setupCommands():void
      {
         // 命令将在executeCommand方法中处理
      }
      
      /**
       * 打印欢迎信息
       */
      private function printWelcome():void
      {
         this.println("=== 龙斗士游戏测试工具控制台 ===");
         this.println("版本: 1.0");
         this.println("输入 'help' 查看可用命令");
         this.println("输入 'connect <ip> <port> <zone> <user> <pass>' 连接服务器");
         this.println("========================================");
      }
      
      /**
       * 执行命令
       */
      private function executeCommand(command:String):void
      {
         this.println("> " + command);
         
         var parts:Array = command.split(" ");
         var cmd:String = parts[0].toLowerCase();
         
         switch(cmd)
         {
            case "help":
               this.showHelp();
               break;
               
            case "connect":
               if(parts.length >= 6)
               {
                  this.connectToServer(parts[1], parseInt(parts[2]), parts[3], parts[4], parts[5]);
               }
               else
               {
                  this.println("用法: connect <ip> <port> <zone> <username> <password>");
               }
               break;
               
            case "disconnect":
               this.disconnectFromServer();
               break;
               
            case "status":
               this.showStatus();
               break;
               
            case "log":
               if(parts.length > 1 && parts[1] == "on")
               {
                  this.serverClient.enablePacketLogging();
                  this.println("数据包日志记录已启用");
               }
               else if(parts.length > 1 && parts[1] == "off")
               {
                  this.serverClient.disablePacketLogging();
                  this.println("数据包日志记录已禁用");
               }
               else
               {
                  this.println("用法: log <on|off>");
               }
               break;
               
            case "send":
               if(parts.length > 1)
               {
                  var hexData:String = parts.slice(1).join(" ");
                  this.serverClient.sendCustomPacket(hexData);
                  this.println("已发送自定义数据包: " + hexData);
               }
               else
               {
                  this.println("用法: send <hex_data>");
               }
               break;
               
            case "vuln":
               if(parts.length > 1)
               {
                  this.serverClient.enableVulnTesting(parts[1]);
                  this.println("已启用漏洞测试: " + parts[1]);
               }
               else
               {
                  this.println("用法: vuln <overflow|injection|malformed|replay>");
               }
               break;
               
            case "stress":
               if(parts.length > 1)
               {
                  var count:int = parseInt(parts[1]);
                  this.serverClient.stressTest(count);
                  this.println("已启动压力测试，发送 " + count + " 个数据包");
               }
               else
               {
                  this.println("用法: stress <packet_count>");
               }
               break;
               
            case "fuzz":
               this.serverClient.fuzzTest();
               this.println("已发送模糊测试数据包");
               break;
               
            case "batch":
               if(parts.length > 2)
               {
                  var batchCount:int = parseInt(parts[1]);
                  var delay:int = parseInt(parts[2]);
                  this.serverClient.batchTest(batchCount, delay);
                  this.println("已启动批量测试: " + batchCount + " 个数据包，间隔 " + delay + "ms");
               }
               else
               {
                  this.println("用法: batch <count> <delay_ms>");
               }
               break;
               
            case "replay":
               if(parts.length > 1)
               {
                  var index:int = parseInt(parts[1]);
                  this.serverClient.replayPacket(index);
                  this.println("已重放数据包 #" + index);
               }
               else
               {
                  this.println("用法: replay <packet_index>");
               }
               break;
               
            case "export":
               var logData:String = this.serverClient.exportPacketLog();
               this.println("数据包日志:");
               this.println(logData);
               break;
               
            case "clear":
               if(parts.length > 1 && parts[1] == "log")
               {
                  this.serverClient.clearPacketLog();
                  this.println("数据包日志已清空");
               }
               else if(parts.length > 1 && parts[1] == "screen")
               {
                  this.outputField.text = "";
               }
               else
               {
                  this.println("用法: clear <log|screen>");
               }
               break;
               
            case "exit":
            case "quit":
               this.cleanup();
               break;
               
            default:
               this.println("未知命令: " + cmd + "，输入 'help' 查看帮助");
               break;
         }
      }
      
      /**
       * 显示帮助信息
       */
      private function showHelp():void
      {
         this.println("可用命令:");
         this.println("  connect <ip> <port> <zone> <user> <pass> - 连接到服务器");
         this.println("  disconnect                               - 断开连接");
         this.println("  status                                   - 显示连接状态");
         this.println("  log <on|off>                            - 启用/禁用数据包日志");
         this.println("  send <hex_data>                         - 发送自定义数据包");
         this.println("  vuln <type>                             - 启用漏洞测试");
         this.println("  stress <count>                          - 压力测试");
         this.println("  fuzz                                    - 模糊测试");
         this.println("  batch <count> <delay>                   - 批量测试");
         this.println("  replay <index>                          - 重放数据包");
         this.println("  export                                  - 导出数据包日志");
         this.println("  clear <log|screen>                      - 清空日志或屏幕");
         this.println("  help                                    - 显示此帮助");
         this.println("  exit/quit                               - 退出程序");
      }
      
      /**
       * 连接到服务器
       */
      private function connectToServer(ip:String, port:int, zone:String, username:String, password:String):void
      {
         this.println("正在连接到 " + ip + ":" + port + "...");
         this.serverClient.login(ip, port, zone, username, password);
      }
      
      /**
       * 断开服务器连接
       */
      private function disconnectFromServer():void
      {
         this.serverClient.disconnect();
         this.println("已断开连接");
      }
      
      /**
       * 显示状态信息
       */
      private function showStatus():void
      {
         var stats:Object = this.serverClient.getStats();
         this.println("=== 连接状态 ===");
         this.println("连接状态: " + (stats.isConnected ? "已连接" : "未连接"));
         this.println("用户ID: " + stats.myUserId);
         this.println("用户名: " + stats.myUserName);
         this.println("房间ID: " + stats.activeRoomId);
         this.println("发送数据包: " + stats.packetsSent);
         this.println("接收数据包: " + stats.packetsReceived);
         this.println("是否空闲: " + (stats.isIdle ? "是" : "否"));
      }
      
      /**
       * 打印文本到输出区域
       */
      private function println(text:String):void
      {
         this.outputField.appendText(text + "\n");
         this.outputField.scrollV = this.outputField.maxScrollV;
      }
      
      /**
       * 清理资源
       */
      private function cleanup():void
      {
         this.serverClient.removeInterceptor();
         this.serverClient.disablePacketLogging();
         this.serverClient.disconnect();
         this.println("程序已退出");
      }
   }
}
